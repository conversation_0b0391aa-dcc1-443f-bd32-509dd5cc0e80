# Epic: Sistema de Status Consistente e Automático - Correção de Inconsistências e Sincronização

## Overview

O sistema de campanhas WhatsApp apresenta inconsistências críticas de status que comprometem a confiabilidade e experiência do usuário. Campanhas com 100% de mensagens enviadas/entregues mantêm status incorretos como `is_sending=true`, `is_sent=false`, `message_count=0`, criando dados contraditórios entre enum status e campos boolean legacy.

Esta Epic foca exclusivamente na correção desses problemas de status, implementando sincronização automática baseada no estado real das mensagens e sistema de monitoramento para detectar campanhas "presas" em status intermediário.

### Funcionalidades Atuais Identificadas:

- ✅ Enum CampaignStatus com estados definidos (DRAFT, SENDING, COMPLETED, FAILED, CANCELLED)
- ✅ Campos boolean legacy (is_sent, is_sending, is_scheduled) para compatibilidade
- ✅ Método getCurrentStatus() que deriva status dos booleans quando enum não existe
- ✅ Sistema básico de contagem de mensagens por campanha
- ⚠️ Sincronização entre enum status e campos boolean (inconsistente)
- ⚠️ Atualização de message_count (não reflete realidade)
- ⚠️ Status de campanhas baseado em estado das mensagens (manual)
- ❌ Job automático para sincronização de status
- ❌ Detecção de campanhas "presas" em status intermediário
- ❌ Logs de sincronização para auditoria
- ❌ Alertas para campanhas com problemas de status

### Melhorias Propostas:

#### 1. **Sincronização Automática de Status**
Implementar job que analisa estado real das mensagens de cada campanha e atualiza status automaticamente, eliminando inconsistências entre enum e campos boolean. Job executa a cada 5 minutos para campanhas não finalizadas.

#### 2. **Detecção de Campanhas Problemáticas**
Sistema para identificar campanhas "presas" em status intermediário por muito tempo (>24h em SENDING, >7 dias sem mudança), gerando alertas automáticos para investigação.

#### 3. **Correção de Message Count**
Garantir que message_count sempre reflita o número real de mensagens da campanha, sendo atualizado automaticamente durante sincronização de status.

#### 4. **Sistema de Logs de Sincronização**
Registrar todas as mudanças de status automáticas com detalhes das estatísticas de mensagens que motivaram a mudança, permitindo auditoria e debugging.

#### 5. **Validação e Correção de Dados Legacy**
Corrigir inconsistências existentes na base de dados e implementar validações para prevenir novos problemas de sincronização.

## Resumo do Plano de Implementação

A Epic será implementada em 3 fases sequenciais, priorizando correção de problemas críticos primeiro, seguida por monitoramento e otimizações.

**Fase 1**: Implementação da Sincronização - Criar job de sincronização automática e corrigir lógica de status
**Fase 2**: Detecção de Problemas - Implementar sistema para detectar campanhas problemáticas e gerar alertas
**Fase 3**: Otimização e Limpeza - Corrigir dados legacy, otimizar performance e implementar limpeza automática

## Plano de Implementação Detalhado

### 1. Implementação da Sincronização Automática

Esta fase implementa o core da solução: job automático que sincroniza status de campanhas baseado no estado real das mensagens.

#### Rotas/Endpoints Necessários:
- `GET /api/campaigns/{id}/status-sync` - Forçar sincronização manual de status específica
- `POST /api/campaigns/{id}/fix-status` - Corrigir status manualmente (admin apenas)
- `GET /api/campaigns/{id}/sync-history` - Histórico de sincronizações da campanha

#### Database:
- **Tabela `campaign_status_sync_logs`**: Log de sincronizações. Campos: id, campaign_id, old_status, new_status, sync_reason, message_stats_json, execution_time_ms, synced_at, created_at
- **Migração `campaigns`**: Adicionar índices: (organization_id, status), (status, updated_at), (is_sending, updated_at)
- **Migração `messages`**: Adicionar índice composto: (campaign_id, status, created_at)

#### Domínios:
- **CampaignStatusSyncLog**: Gerenciar logs de sincronização com métodos para análise
- **CampaignStatusAnalyzer**: Analisar estatísticas de mensagens e determinar status correto
- **StatusSyncExecutor**: Executar sincronização com validações e rollback

#### Usecases:
- **Campaign/SyncStatusFromMessages**: Sincronizar status baseado no estado real das mensagens
- **Campaign/AnalyzeMessageStats**: Analisar estatísticas de mensagens para determinar status
- **Campaign/GetSyncHistory**: Histórico de sincronizações de uma campanha
- **Campaign/ForceSyncStatus**: Forçar sincronização manual com validações
- **Campaign/ValidateStatusConsistency**: Validar consistência entre enum e booleans

#### Jobs/Cron:
- **SyncCampaignStatusFromMessages**: Executa a cada 5 minutos, processa campanhas não finalizadas

### 2. Detecção de Campanhas Problemáticas

Esta fase implementa sistema de monitoramento para detectar campanhas com problemas de status e gerar alertas automáticos.

#### Rotas/Endpoints Necessários:
- `GET /api/campaigns/stuck` - Listar campanhas com problemas de status
- `GET /api/campaigns/health-check` - Verificação geral de saúde das campanhas
- `POST /api/campaigns/bulk-fix-status` - Corrigir múltiplas campanhas (admin)

#### Database:
- **Tabela `stuck_campaign_alerts`**: Alertas de campanhas problemáticas. Campos: id, campaign_id, problem_type, detected_at, severity, auto_fixed, manual_action_required, resolved_at, created_at
- **Migração `campaigns`**: Adicionar campos: last_status_check_at, status_inconsistency_count

#### Domínios:
- **StuckCampaignDetector**: Detectar campanhas com problemas usando critérios específicos
- **CampaignHealthChecker**: Verificar saúde geral das campanhas
- **AlertManager**: Gerenciar alertas e notificações

#### Usecases:
- **Campaign/DetectStuckCampaigns**: Identificar campanhas com status problemático
- **Campaign/GetStuckCampaigns**: Buscar campanhas problemáticas com filtros
- **Campaign/CheckCampaignHealth**: Verificar saúde de campanha específica
- **Campaign/BulkFixStatus**: Corrigir múltiplas campanhas simultaneamente
- **Alert/ProcessStuckCampaignAlerts**: Processar e enviar alertas

#### Jobs/Cron:
- **DetectStuckCampaigns**: Executa diariamente, identifica campanhas problemáticas
- **ProcessStuckCampaignAlerts**: Executa a cada hora, processa alertas pendentes

### 3. Otimização e Limpeza de Dados

Esta fase corrige dados legacy, otimiza performance e implementa limpeza automática de logs antigos.

#### Rotas/Endpoints Necessários:
- `POST /api/admin/campaigns/fix-legacy-data` - Corrigir dados inconsistentes (admin)
- `GET /api/admin/campaigns/consistency-report` - Relatório de consistência geral

#### Database:
- **Migração de correção**: Script para corrigir dados inconsistentes existentes
- **Otimização de índices**: Adicionar índices específicos para performance

#### Domínios:
- **LegacyDataFixer**: Corrigir inconsistências em dados existentes
- **ConsistencyReporter**: Gerar relatórios de consistência
- **PerformanceOptimizer**: Otimizar queries e performance

#### Usecases:
- **Admin/FixLegacyData**: Corrigir dados inconsistentes em lote
- **Admin/GenerateConsistencyReport**: Relatório detalhado de consistência
- **Campaign/OptimizeStatusQueries**: Otimizar queries de status
- **Maintenance/CleanupOldSyncLogs**: Limpar logs antigos

#### Jobs/Cron:
- **CleanupStatusSyncLogs**: Executa semanalmente, remove logs >30 dias
- **ValidateDataConsistency**: Executa semanalmente, valida consistência geral

## Previsão de PR

### Enums
```
app/Enums/CampaignSyncReason.php (novo)
app/Enums/StuckCampaignProblemType.php (novo)
app/Enums/AlertSeverity.php (novo)
```

### Models
```
app/Models/CampaignStatusSyncLog.php (novo)
app/Models/StuckCampaignAlert.php (novo)
app/Models/Campaign.php (modificado - novos métodos e campos)
```

### Domains
```
app/Domains/ChatBot/CampaignStatusSyncLog.php (novo)
app/Domains/ChatBot/CampaignStatusAnalyzer.php (novo)
app/Domains/ChatBot/StatusSyncExecutor.php (novo)
app/Domains/ChatBot/StuckCampaignDetector.php (novo)
app/Domains/ChatBot/CampaignHealthChecker.php (novo)
app/Domains/ChatBot/AlertManager.php (novo)
app/Domains/ChatBot/LegacyDataFixer.php (novo)
app/Domains/ChatBot/ConsistencyReporter.php (novo)
app/Domains/ChatBot/PerformanceOptimizer.php (novo)
app/Domains/ChatBot/Campaign.php (modificado - novos métodos)
```

### Factories
```
app/Factories/ChatBot/CampaignStatusSyncLogFactory.php (novo)
app/Factories/ChatBot/StuckCampaignAlertFactory.php (novo)
app/Factories/ChatBot/CampaignFactory.php (modificado)
```

### Repositories
```
app/Repositories/CampaignStatusSyncLogRepository.php (novo)
app/Repositories/StuckCampaignAlertRepository.php (novo)
app/Repositories/CampaignRepository.php (modificado - novos métodos)
```

### Use Cases
```
app/UseCases/ChatBot/Campaign/SyncStatusFromMessages.php (novo)
app/UseCases/ChatBot/Campaign/AnalyzeMessageStats.php (novo)
app/UseCases/ChatBot/Campaign/GetSyncHistory.php (novo)
app/UseCases/ChatBot/Campaign/ForceSyncStatus.php (novo)
app/UseCases/ChatBot/Campaign/ValidateStatusConsistency.php (novo)
app/UseCases/ChatBot/Campaign/DetectStuckCampaigns.php (novo)
app/UseCases/ChatBot/Campaign/GetStuckCampaigns.php (novo)
app/UseCases/ChatBot/Campaign/CheckCampaignHealth.php (novo)
app/UseCases/ChatBot/Campaign/BulkFixStatus.php (novo)
app/UseCases/ChatBot/Alert/ProcessStuckCampaignAlerts.php (novo)
app/UseCases/ChatBot/Admin/FixLegacyData.php (novo)
app/UseCases/ChatBot/Admin/GenerateConsistencyReport.php (novo)
app/UseCases/ChatBot/Campaign/OptimizeStatusQueries.php (novo)
app/UseCases/ChatBot/Maintenance/CleanupOldSyncLogs.php (novo)
```

### Controllers
```
app/Http/Controllers/ChatBot/CampaignController.php (modificado - novos endpoints)
app/Http/Controllers/ChatBot/CampaignStatusController.php (novo)
app/Http/Controllers/Admin/CampaignMaintenanceController.php (novo)
```

### Requests
```
app/Http/Requests/Campaign/ForceSyncStatusRequest.php (novo)
app/Http/Requests/Campaign/BulkFixStatusRequest.php (novo)
app/Http/Requests/Admin/FixLegacyDataRequest.php (novo)
```

### Jobs
```
app/Jobs/SyncCampaignStatusFromMessages.php (novo)
app/Jobs/DetectStuckCampaigns.php (novo)
app/Jobs/ProcessStuckCampaignAlerts.php (novo)
app/Jobs/CleanupStatusSyncLogs.php (novo)
app/Jobs/ValidateDataConsistency.php (novo)
```

### Migrations
```
database/migrations/xxxx_create_campaign_status_sync_logs_table.php (novo)
database/migrations/xxxx_create_stuck_campaign_alerts_table.php (novo)
database/migrations/xxxx_add_status_sync_indexes_to_campaigns_table.php (novo)
database/migrations/xxxx_add_status_fields_to_campaigns_table.php (novo)
database/migrations/xxxx_add_message_indexes_for_status_sync.php (novo)
database/migrations/xxxx_fix_legacy_campaign_status_data.php (novo)
```

### Tests
```
tests/Unit/UseCases/ChatBot/Campaign/SyncStatusFromMessagesTest.php (novo)
tests/Unit/Domains/ChatBot/CampaignStatusAnalyzerTest.php (novo)
tests/Unit/Domains/ChatBot/StuckCampaignDetectorTest.php (novo)
tests/Unit/Jobs/SyncCampaignStatusFromMessagesTest.php (novo)
tests/Feature/Campaign/StatusSyncTest.php (novo)
tests/Feature/Campaign/StuckCampaignDetectionTest.php (novo)
```

**Total Estimado: ~45 arquivos (38 novos + 7 modificados)**

## Melhorias Específicas Identificadas

### Problema 1: Status Inconsistente Entre Enum e Booleans
**Situação Atual**: Campanhas apresentam dados contraditórios como `status=1` (DRAFT), `is_sending=true`, `sent_at` preenchido e `is_sent=false` simultaneamente. O enum CampaignStatus não sincroniza automaticamente com campos boolean legacy.
**Impacto**: Usuários veem status incorretos na interface, relatórios ficam inconsistentes, campanhas aparecem como "enviando" indefinidamente, e troubleshooting torna-se impossível. Suporte recebe tickets constantes sobre campanhas "travadas".
**Solução**: Job `SyncCampaignStatusFromMessages` que analisa estado real das mensagens a cada 5 minutos e sincroniza enum status com campos boolean, garantindo consistência automática.

### Problema 2: Message Count Incorreto
**Situação Atual**: Campo `message_count` frequentemente mostra 0 mesmo para campanhas com mensagens enviadas, não sendo atualizado automaticamente quando mensagens são geradas ou processadas.
**Impacto**: Métricas incorretas em dashboards, relatórios de performance inválidos, impossibilidade de calcular taxas de sucesso reais, e decisões de negócio baseadas em dados incorretos.
**Solução**: Atualização automática do `message_count` durante sincronização de status, garantindo que sempre reflita o número real de mensagens da campanha.

### Problema 3: Campanhas "Presas" Não Detectadas
**Situação Atual**: Campanhas ficam em status intermediário (SENDING) por dias ou semanas sem detecção automática, mesmo quando todas as mensagens já foram processadas ou falharam definitivamente.
**Impacto**: Recursos computacionais desperdiçados em campanhas que não progredirão, usuários confusos sobre status real, e acúmulo de campanhas problemáticas na base de dados.
**Solução**: Sistema de detecção automática que identifica campanhas presas usando critérios temporais e de estado das mensagens, gerando alertas para investigação.

## Plano de Testes

### Testes Unitários:
- Lógica de determinação de status baseado em estatísticas de mensagens (sent, failed, pending)
- Algoritmo de detecção de campanhas "presas" com diferentes critérios temporais
- Sincronização entre enum CampaignStatus e campos boolean legacy
- Cálculo correto de message_count baseado em mensagens reais
- Validações de consistência entre diferentes campos de status
- Geração de logs de sincronização com dados corretos
- Classificação de problemas de campanhas por tipo e severidade

### Testes de Integração:
- Fluxo completo de sincronização: análise de mensagens → determinação de status → atualização de campanha → log
- Job SyncCampaignStatusFromMessages processando múltiplas campanhas simultaneamente
- Detecção de campanhas problemáticas e geração de alertas automáticos
- Correção de dados legacy sem afetar campanhas ativas
- Integração com sistema de notificações para alertas críticos
- Performance de queries de sincronização com grandes volumes de dados

### Testes de Performance:
- Job processando 1000+ campanhas em menos de 5 minutos
- Queries de análise de mensagens otimizadas para campanhas com 100k+ mensagens
- Sistema de detecção de campanhas presas sem impacto na performance geral
- Limpeza de logs antigos sem afetar operações em andamento
- Índices de banco otimizados para consultas de status frequentes

### Testes de Regressão:
- Envio normal de campanhas continua funcionando sem alterações
- Sistema de retry existente mantém compatibilidade total
- APIs existentes de campanha mantêm mesma interface e comportamento
- Dados históricos de campanhas permanecem íntegros após correções
- Webhooks de status continuam sendo processados corretamente
- Funcionalidades de filtros e busca de campanhas não são afetadas

## Conclusão

Esta Epic estabelece a fundação de confiabilidade para todo o sistema de campanhas WhatsApp, eliminando inconsistências críticas que afetam a experiência do usuário e a operação do sistema. A implementação de sincronização automática e detecção proativa de problemas transformará um sistema problemático em uma plataforma confiável.

### Benefícios Esperados:
- **Confiabilidade**: Status sempre consistentes e atualizados automaticamente
- **Transparência**: Usuários sempre veem o estado real das campanhas
- **Eficiência Operacional**: Redução drástica de tickets de suporte relacionados a status
- **Qualidade de Dados**: Métricas e relatórios sempre precisos
- **Detecção Proativa**: Problemas identificados automaticamente antes de afetar usuários
- **Manutenibilidade**: Sistema auto-corretivo que previne acúmulo de inconsistências

### Impacto no Negócio:
- Redução de 90% em tickets de suporte relacionados a status inconsistente
- Eliminação completa de campanhas "travadas" por mais de 24 horas
- Melhoria de 100% na precisão de métricas de campanhas
- Redução de 80% no tempo de investigação de problemas de status
- Aumento significativo na confiança dos usuários no sistema
- Base sólida para implementação de funcionalidades futuras

### Métricas de Sucesso:
- 100% das campanhas com status consistente entre enum e booleans
- 0 campanhas em status SENDING por mais de 24h sem progresso
- message_count sempre igual ao número real de mensagens
- Detecção automática de 95% dos problemas de status
- Tempo de sincronização <30 segundos para campanhas com até 10k mensagens
- Redução de 95% em falsos positivos de campanhas problemáticas

## Referências

- Laravel Job Scheduling Documentation - https://laravel.com/docs/scheduling
- Database Indexing Best Practices - https://use-the-index-luke.com/
- Enum vs Boolean Fields Performance - https://dev.mysql.com/doc/refman/8.0/en/enum.html
- Campaign Status Management Patterns - Design patterns internos do projeto
- Epic Campanhas WhatsApp v2.0 (documento base) - storage/internal_confluence/schedule/EPICs/ChatBot/campanhas-whatsapp-v2.md
- Documentação Campanhas WhatsApp Atual - storage/internal_confluence/services/Meta/campanhas-whatsapp.md
