# Epic: Sistema de Agendamento Robusto - Campanhas Programadas com Precisão

## Overview

O sistema atual possui lógica básica de agendamento através do campo `scheduled_at` nas mensagens, mas carece de interface completa, validações robustas, e garantias de execução no horário correto. Esta Epic implementa sistema completo de agendamento de campanhas com validações de timezone, interface de usuário, processamento automático e monitoramento de execução.

O agendamento robusto permitirá que usuários programem campanhas para horários específicos com confiança total de execução, incluindo validações de timezone, notificações de campanhas próximas, e histórico completo de agendamentos para auditoria e controle.

### Funcionalidades Atuais Identificadas:

- ✅ Campo `scheduled_at` no model Message
- ✅ Lógica básica em `GetMessagesAvailableToSent` que respeita `scheduled_at <= now()`
- ✅ Job `SendWhatsAppMessages` executando a cada minuto
- ✅ Campos `is_scheduled` e `scheduled_at` no model Campaign
- ✅ Enum CampaignStatus com estado SCHEDULED
- ⚠️ Validação de agendamento (básica, sem timezone)
- ⚠️ Interface para agendar campanhas (inexistente)
- ❌ Endpoint específico para agendamento
- ❌ Validações de timezone e conversão
- ❌ Job específico para processar campanhas agendadas
- ❌ Histórico de agendamentos e mudanças
- ❌ Notificações de campanhas próximas
- ❌ Validação de horários de negócio
- ❌ Interface para gerenciar agendamentos
- ❌ Reagendamento de campanhas

### Melhorias Propostas:

#### 1. **Endpoint Completo de Agendamento**
Implementar endpoints para agendar, reagendar e cancelar agendamento de campanhas com validações completas de timezone, horário futuro e regras de negócio.

#### 2. **Sistema de Timezone Robusto**
Validação e conversão automática de timezones, permitindo que usuários agendem campanhas em seu fuso horário local com execução precisa no horário correto.

#### 3. **Job Específico para Campanhas Agendadas**
Criar job dedicado para processar campanhas agendadas, separado do envio normal, com validações específicas e logs detalhados.

#### 4. **Histórico e Auditoria de Agendamentos**
Registrar todas as mudanças de agendamento (criação, modificação, cancelamento) para auditoria completa e troubleshooting.

#### 5. **Sistema de Notificações e Alertas**
Notificar usuários sobre campanhas que serão executadas em breve e alertar sobre problemas de agendamento.

## Resumo do Plano de Implementação

A Epic será implementada em 3 fases focadas em funcionalidade core, seguida por melhorias e interface de usuário.

**Fase 1**: Core de Agendamento - Implementar endpoints e lógica fundamental de agendamento
**Fase 2**: Processamento e Monitoramento - Job dedicado e sistema de monitoramento
**Fase 3**: Interface e Notificações - Interface completa e sistema de notificações

## Plano de Implementação Detalhado

### 1. Core de Agendamento

Esta fase implementa a funcionalidade fundamental de agendamento com validações completas.

#### Rotas/Endpoints Necessários:
- `POST /api/campaigns/{id}/schedule` - Agendar campanha para data/hora específica
- `PUT /api/campaigns/{id}/reschedule` - Reagendar campanha já agendada
- `DELETE /api/campaigns/{id}/unschedule` - Cancelar agendamento
- `GET /api/campaigns/{id}/schedule-info` - Informações de agendamento da campanha

#### Database:
- **Tabela `campaign_schedule_history`**: Histórico de agendamentos. Campos: id, campaign_id, old_scheduled_at, new_scheduled_at, timezone, action (schedule/reschedule/unschedule), reason, user_id, created_at
- **Migração `campaigns`**: Adicionar campos: timezone VARCHAR(50), schedule_notes TEXT, scheduled_by_user_id INT, schedule_validation_errors JSON

#### Domínios:
- **CampaignScheduler**: Gerenciar agendamento com validações completas
- **TimezoneValidator**: Validar e converter timezones corretamente
- **ScheduleValidator**: Validar regras de agendamento (horário futuro, horário comercial, etc.)
- **ScheduleHistory**: Rastrear histórico de mudanças de agendamento

#### Usecases:
- **Campaign/Schedule**: Agendar campanha com validações completas
- **Campaign/Reschedule**: Reagendar campanha existente
- **Campaign/Unschedule**: Cancelar agendamento de campanha
- **Campaign/ValidateScheduleTime**: Validar se horário de agendamento é válido
- **Campaign/GetScheduleInfo**: Informações detalhadas de agendamento
- **Schedule/ConvertTimezone**: Converter horários entre timezones

#### Jobs/Cron:
- **ValidateScheduledCampaigns**: Executa a cada hora, valida campanhas agendadas para próximas 24h

### 2. Processamento e Monitoramento de Campanhas Agendadas

Esta fase implementa job dedicado para processar campanhas agendadas e sistema de monitoramento.

#### Rotas/Endpoints Necessários:
- `GET /api/campaigns/scheduled` - Listar campanhas agendadas com filtros
- `GET /api/campaigns/{id}/schedule-history` - Histórico completo de agendamentos
- `GET /api/admin/scheduled-campaigns/monitor` - Monitoramento de campanhas agendadas (admin)

#### Database:
- **Tabela `scheduled_campaign_executions`**: Log de execuções. Campos: id, campaign_id, scheduled_for, executed_at, execution_delay_seconds, success, error_message, messages_generated, created_at
- **Índices**: (scheduled_at, status), (organization_id, scheduled_at), (timezone, scheduled_at)

#### Domínios:
- **ScheduledCampaignProcessor**: Processar campanhas que chegaram no horário
- **ScheduleMonitor**: Monitorar execução de campanhas agendadas
- **ExecutionLogger**: Registrar logs de execução detalhados
- **SchedulePerformanceAnalyzer**: Analisar performance de execução

#### Usecases:
- **Campaign/ProcessScheduledCampaigns**: Processar campanhas que chegaram no horário
- **Campaign/GetScheduled**: Buscar campanhas agendadas com filtros
- **Campaign/GetScheduleHistory**: Histórico de agendamentos de uma campanha
- **Schedule/MonitorExecution**: Monitorar execução de campanhas agendadas
- **Schedule/AnalyzePerformance**: Analisar performance de agendamentos

#### Jobs/Cron:
- **ProcessScheduledCampaigns**: Executa a cada minuto, processa campanhas no horário
- **MonitorScheduledCampaigns**: Executa a cada 15 minutos, monitora execução

### 3. Interface e Sistema de Notificações

Esta fase implementa interface completa e sistema de notificações para campanhas agendadas.

#### Rotas/Endpoints Necessários:
- `GET /api/campaigns/upcoming` - Campanhas que serão executadas em breve
- `GET /api/organization/schedule-dashboard` - Dashboard de agendamentos
- `POST /api/campaigns/{id}/schedule-notification` - Configurar notificações
- `GET /api/timezones/supported` - Lista de timezones suportados

#### Database:
- **Tabela `schedule_notifications`**: Configurações de notificação. Campos: id, campaign_id, user_id, notify_before_minutes, notification_sent, sent_at, created_at
- **Migração `users`**: Adicionar campos: default_timezone VARCHAR(50), schedule_notifications_enabled BOOLEAN DEFAULT true

#### Domínios:
- **ScheduleNotificationManager**: Gerenciar notificações de agendamento
- **UpcomingCampaignDetector**: Detectar campanhas próximas de execução
- **ScheduleDashboard**: Gerar dados para dashboard de agendamentos
- **TimezoneManager**: Gerenciar timezones suportados

#### Usecases:
- **Campaign/GetUpcoming**: Buscar campanhas próximas de execução
- **Schedule/GetDashboardData**: Dados para dashboard de agendamentos
- **Notification/ConfigureScheduleNotification**: Configurar notificações
- **Notification/SendUpcomingCampaignAlerts**: Enviar alertas de campanhas próximas
- **Timezone/GetSupported**: Lista de timezones suportados
- **Schedule/GetUserPreferences**: Preferências de agendamento do usuário

#### Jobs/Cron:
- **NotifyUpcomingCampaigns**: Executa a cada hora, notifica campanhas próximas
- **CleanupScheduleHistory**: Executa semanalmente, limpa histórico antigo

## Previsão de PR

### Enums
```
app/Enums/ScheduleAction.php (novo)
app/Enums/ScheduleValidationError.php (novo)
app/Enums/NotificationTiming.php (novo)
```

### Models
```
app/Models/CampaignScheduleHistory.php (novo)
app/Models/ScheduledCampaignExecution.php (novo)
app/Models/ScheduleNotification.php (novo)
app/Models/Campaign.php (modificado - campos de agendamento)
app/Models/User.php (modificado - preferências de timezone)
```

### Domains
```
app/Domains/ChatBot/CampaignScheduler.php (novo)
app/Domains/ChatBot/TimezoneValidator.php (novo)
app/Domains/ChatBot/ScheduleValidator.php (novo)
app/Domains/ChatBot/ScheduleHistory.php (novo)
app/Domains/ChatBot/ScheduledCampaignProcessor.php (novo)
app/Domains/ChatBot/ScheduleMonitor.php (novo)
app/Domains/ChatBot/ExecutionLogger.php (novo)
app/Domains/ChatBot/SchedulePerformanceAnalyzer.php (novo)
app/Domains/ChatBot/ScheduleNotificationManager.php (novo)
app/Domains/ChatBot/UpcomingCampaignDetector.php (novo)
app/Domains/ChatBot/ScheduleDashboard.php (novo)
app/Domains/ChatBot/TimezoneManager.php (novo)
```

### Factories
```
app/Factories/ChatBot/CampaignScheduleHistoryFactory.php (novo)
app/Factories/ChatBot/ScheduledCampaignExecutionFactory.php (novo)
app/Factories/ChatBot/ScheduleNotificationFactory.php (novo)
```

### Repositories
```
app/Repositories/CampaignScheduleHistoryRepository.php (novo)
app/Repositories/ScheduledCampaignExecutionRepository.php (novo)
app/Repositories/ScheduleNotificationRepository.php (novo)
app/Repositories/CampaignRepository.php (modificado - métodos de agendamento)
```

### Use Cases
```
app/UseCases/ChatBot/Campaign/Schedule.php (novo)
app/UseCases/ChatBot/Campaign/Reschedule.php (novo)
app/UseCases/ChatBot/Campaign/Unschedule.php (novo)
app/UseCases/ChatBot/Campaign/ValidateScheduleTime.php (novo)
app/UseCases/ChatBot/Campaign/GetScheduleInfo.php (novo)
app/UseCases/ChatBot/Campaign/ProcessScheduledCampaigns.php (novo)
app/UseCases/ChatBot/Campaign/GetScheduled.php (novo)
app/UseCases/ChatBot/Campaign/GetScheduleHistory.php (novo)
app/UseCases/ChatBot/Campaign/GetUpcoming.php (novo)
app/UseCases/ChatBot/Schedule/ConvertTimezone.php (novo)
app/UseCases/ChatBot/Schedule/MonitorExecution.php (novo)
app/UseCases/ChatBot/Schedule/AnalyzePerformance.php (novo)
app/UseCases/ChatBot/Schedule/GetDashboardData.php (novo)
app/UseCases/ChatBot/Schedule/GetUserPreferences.php (novo)
app/UseCases/ChatBot/Notification/ConfigureScheduleNotification.php (novo)
app/UseCases/ChatBot/Notification/SendUpcomingCampaignAlerts.php (novo)
app/UseCases/ChatBot/Timezone/GetSupported.php (novo)
```

### Controllers
```
app/Http/Controllers/ChatBot/CampaignController.php (modificado - endpoints de agendamento)
app/Http/Controllers/ChatBot/CampaignScheduleController.php (novo)
app/Http/Controllers/ChatBot/ScheduleDashboardController.php (novo)
app/Http/Controllers/ChatBot/TimezoneController.php (novo)
```

### Requests
```
app/Http/Requests/Campaign/ScheduleRequest.php (novo)
app/Http/Requests/Campaign/RescheduleRequest.php (novo)
app/Http/Requests/Schedule/NotificationRequest.php (novo)
```

### Jobs
```
app/Jobs/ProcessScheduledCampaigns.php (novo)
app/Jobs/ValidateScheduledCampaigns.php (novo)
app/Jobs/MonitorScheduledCampaigns.php (novo)
app/Jobs/NotifyUpcomingCampaigns.php (novo)
app/Jobs/CleanupScheduleHistory.php (novo)
```

### Migrations
```
database/migrations/xxxx_create_campaign_schedule_history_table.php (novo)
database/migrations/xxxx_create_scheduled_campaign_executions_table.php (novo)
database/migrations/xxxx_create_schedule_notifications_table.php (novo)
database/migrations/xxxx_add_schedule_fields_to_campaigns_table.php (novo)
database/migrations/xxxx_add_timezone_fields_to_users_table.php (novo)
database/migrations/xxxx_add_schedule_indexes.php (novo)
```

**Total Estimado: ~40 arquivos (37 novos + 3 modificados)**

## Melhorias Específicas Identificadas

### Problema 1: Sistema de Agendamento Não Validado
**Situação Atual**: Existe lógica básica de agendamento através do campo `scheduled_at`, mas não há endpoint para agendar campanhas, validação de timezone, interface de usuário, nem garantia de que funciona corretamente na prática.
**Impacto**: Funcionalidade de agendamento inutilizável, usuários precisam enviar campanhas manualmente no horário desejado, perda de oportunidades de marketing em horários ótimos, e impossibilidade de automação de campanhas.
**Solução**: Endpoint `/schedule` com validações completas, job `ProcessScheduledCampaigns` para processar no horário correto, interface para agendamento, e sistema de notificações.

### Problema 2: Ausência de Validação de Timezone
**Situação Atual**: Sistema não valida ou converte timezones, assumindo sempre UTC ou timezone do servidor, causando execução de campanhas em horários incorretos para usuários em diferentes fusos horários.
**Impacto**: Campanhas executadas em horários inadequados, perda de efetividade por envio fora do horário comercial, confusão dos usuários sobre quando campanhas serão enviadas.
**Solução**: Sistema robusto de validação e conversão de timezone, permitindo agendamento no fuso horário local do usuário com execução precisa.

### Problema 3: Falta de Histórico e Auditoria
**Situação Atual**: Não existe registro de mudanças de agendamento, impossibilitando auditoria de quem agendou/reagendou campanhas e quando, dificultando troubleshooting de problemas.
**Impacto**: Impossibilidade de rastrear mudanças, dificuldade para identificar problemas de agendamento, falta de accountability sobre agendamentos, e debugging complexo.
**Solução**: Sistema completo de histórico registrando todas as mudanças com usuário, horário, e motivo, permitindo auditoria completa.

## Plano de Testes

### Testes Unitários:
- Validação e conversão de timezones para diferentes fusos horários
- Lógica de determinação de campanhas prontas para execução
- Validações de horário futuro e regras de negócio
- Processamento de campanhas agendadas no horário exato
- Geração de notificações para campanhas próximas
- Cálculo de delays de execução e métricas de performance
- Validações de entrada para endpoints de agendamento

### Testes de Integração:
- Fluxo completo de agendamento: criação → validação → processamento → execução
- Job ProcessScheduledCampaigns executando campanhas no horário correto
- Sistema de notificações enviando alertas no tempo adequado
- Histórico de agendamentos sendo registrado corretamente
- Conversão de timezone funcionando com diferentes fusos horários
- Dashboard de agendamentos exibindo dados precisos

### Testes de Performance:
- Job processando 100+ campanhas agendadas simultaneamente
- Queries de busca de campanhas agendadas otimizadas
- Sistema de notificações com baixa latência
- Dashboard carregando rapidamente mesmo com muitos agendamentos
- Validações de timezone com performance adequada

### Testes de Regressão:
- Sistema de envio normal de campanhas não é afetado
- Campanhas não agendadas continuam funcionando normalmente
- APIs existentes de campanha mantêm compatibilidade
- Dados históricos de campanhas permanecem íntegros
- Sistema de templates não é impactado pelas mudanças

## Conclusão

Esta Epic transforma o agendamento de campanhas de uma funcionalidade básica e não confiável em um sistema robusto e profissional. A implementação de validações de timezone, interface completa e monitoramento de execução permitirá que usuários confiem plenamente no agendamento de suas campanhas.

### Benefícios Esperados:
- **Confiabilidade**: Campanhas executadas exatamente no horário agendado
- **Precisão**: Suporte completo a timezones com conversão automática
- **Usabilidade**: Interface intuitiva para agendar e gerenciar campanhas
- **Transparência**: Histórico completo de agendamentos para auditoria
- **Proatividade**: Notificações sobre campanhas próximas de execução
- **Automação**: Possibilidade de automatizar campanhas em horários ótimos

### Impacto no Negócio:
- Aumento de 50% na utilização de agendamento de campanhas
- Melhoria de 90% na precisão de execução no horário correto
- Redução de 80% em campanhas enviadas em horários inadequados
- Aumento de 40% na efetividade de campanhas por timing adequado
- Redução de 70% em tickets sobre problemas de agendamento
- Melhoria significativa na satisfação do usuário

### Métricas de Sucesso:
- 100% das campanhas agendadas executadas no horário correto (±2 minutos)
- Suporte a todos os timezones principais (UTC-12 a UTC+14)
- Histórico completo de agendamentos disponível por 90 dias
- Notificações enviadas com 95% de precisão
- Dashboard de agendamentos atualizado em tempo real
- Zero campanhas perdidas por problemas de agendamento

## Referências

- PHP DateTime and Timezone Documentation - https://www.php.net/manual/en/class.datetimezone.php
- Laravel Task Scheduling - https://laravel.com/docs/scheduling
- Timezone Best Practices - https://stackoverflow.com/questions/2532729/daylight-saving-time-and-time-zone-best-practices
- Campaign Scheduling Patterns - Padrões internos de agendamento
- Epic Campanhas WhatsApp v2.0 (documento base) - storage/internal_confluence/schedule/EPICs/ChatBot/campanhas-whatsapp-v2.md
- Documentação Campanhas WhatsApp Atual - storage/internal_confluence/services/Meta/campanhas-whatsapp.md
