# Epic: Sistema de Campanhas WhatsApp v2.0 - Correção de Status, Agendamento e Logs Avançados

## Overview

O sistema de campanhas WhatsApp v1 está operacional mas apresenta inconsistências críticas que comprometem a confiabilidade e experiência do usuário. Esta Epic visa corrigir problemas fundamentais de status inconsistentes, implementar agendamento robusto, criar endpoint de teste em tempo real e estabelecer sistema de logs avançado para debugging eficiente.

O estado atual revela campanhas com 100% de mensagens enviadas/entregues mantendo status incorretos (is_sending=true, is_sent=false), sistema de agendamento não validado, ausência de endpoint para teste em tempo real e logs de erro insuficientes para troubleshooting eficaz.

### Funcionalidades Atuais Identificadas:

- ✅ Criação e gestão básica de campanhas (Campaign model)
- ✅ Sistema de templates com publicação no WhatsApp
- ✅ Geração automática de mensagens por campanha
- ✅ Envio automatizado via cron job (whatsapp:send-messages)
- ✅ Integração com WhatsApp Business API
- ✅ Webhook para recebimento de status de entrega
- ✅ Sistema básico de status de mensagens (MessageStatus enum)
- ✅ Sistema de retry com backoff exponencial
- ⚠️ Status de campanhas (enum existe mas não sincroniza com booleans)
- ⚠️ Agendamento de campanhas (lógica básica existe mas não validada)
- ⚠️ Logs de erro (básicos, sem detalhes WhatsApp específicos)
- ❌ Sincronização automática de status baseado em mensagens
- ❌ Endpoint de teste em tempo real
- ❌ Sistema de logs avançado com classificação de erros
- ❌ Monitoramento de campanhas "presas"
- ❌ Interface para agendamento de campanhas

### Melhorias Propostas:

#### 1. **Sistema de Status Consistente e Automático**
Implementar sincronização automática de status de campanhas baseado no estado real das mensagens, eliminando inconsistências entre campos boolean legacy e enum status. Incluir job para detectar campanhas "presas" em status intermediário por muito tempo.

#### 2. **Sistema de Agendamento Robusto**
Validar e aprimorar o sistema de agendamento existente, implementando endpoint para agendar campanhas, validações de timezone, e job específico para processar campanhas agendadas no momento correto.

#### 3. **Endpoint de Teste em Tempo Real**
Criar endpoint para envio de teste que permite validar campanhas em tempo real com um número específico, retornando resultado imediato com logs detalhados sem afetar a base de dados de produção.

#### 4. **Sistema de Logs Avançado**
Implementar captura detalhada de erros WhatsApp com códigos específicos, classificação automática de tipos de erro, sugestões de correção e dashboard de monitoramento para troubleshooting eficiente.

#### 5. **Monitoramento e Alertas Automáticos**
Estabelecer sistema de monitoramento proativo com alertas para campanhas com problemas, métricas de performance e dashboard em tempo real para acompanhamento operacional.

## Resumo do Plano de Implementação

A Epic será implementada em 4 fases sequenciais e independentes, cada uma entregando valor incremental e permitindo validação antes da próxima fase. A divisão prioriza correção de problemas críticos primeiro, seguida por funcionalidades novas.

**Fase 1**: Correção de Status - Implementar sincronização automática de status e correção de inconsistências existentes
**Fase 2**: Sistema de Agendamento - Validar e implementar agendamento robusto com interface completa
**Fase 3**: Endpoint de Teste - Criar sistema de teste em tempo real com logs detalhados
**Fase 4**: Logs Avançados - Implementar captura avançada de erros e sistema de monitoramento

## Plano de Implementação Detalhado

### 1. Correção de Status e Sincronização Automática

Esta fase foca na correção dos problemas críticos de status inconsistente, implementando jobs automáticos para sincronização e monitoramento de campanhas problemáticas.

#### Rotas/Endpoints Necessários:
- `GET /api/campaigns/{id}/status-sync` - Forçar sincronização manual de status
- `GET /api/campaigns/stuck` - Listar campanhas com problemas de status
- `POST /api/campaigns/{id}/fix-status` - Corrigir status manualmente (admin)

#### Database:
- **Tabela `campaign_status_sync_logs`**: Log de sincronizações automáticas. Campos: id, campaign_id, old_status, new_status, sync_reason, message_stats_json, synced_at, created_at
- **Migração `campaigns`**: Adicionar índices para otimizar queries de status: (organization_id, status), (status, updated_at)
- **Migração `messages`**: Adicionar índice composto: (campaign_id, status, created_at)

#### Domínios:
- **CampaignStatusSyncLog**: Gerenciar logs de sincronização automática de status
- **CampaignStatusAnalyzer**: Analisar estado das mensagens e determinar status correto
- **StuckCampaignDetector**: Detectar campanhas com problemas de status há muito tempo

#### Usecases:
- **Campaign/SyncStatusFromMessages**: Sincronizar status baseado no estado real das mensagens
- **Campaign/DetectStuckCampaigns**: Identificar campanhas com status problemático
- **Campaign/FixCampaignStatus**: Corrigir status de campanha específica
- **Campaign/GetStatusSyncHistory**: Histórico de sincronizações de uma campanha
- **Campaign/AnalyzeMessageStats**: Analisar estatísticas de mensagens para determinar status

#### Jobs/Cron:
- **SyncCampaignStatusFromMessages**: Executa a cada 5 minutos, sincroniza status de campanhas não finalizadas
- **DetectStuckCampaigns**: Executa diariamente, identifica campanhas presas e envia alertas
- **CleanupStatusSyncLogs**: Executa semanalmente, remove logs antigos (>30 dias)

### 2. Sistema de Agendamento Robusto

Esta fase implementa o sistema completo de agendamento de campanhas com validações de timezone, interface de usuário e processamento automático no horário correto.

#### Rotas/Endpoints Necessários:
- `POST /api/campaigns/{id}/schedule` - Agendar campanha para data/hora específica
- `PUT /api/campaigns/{id}/reschedule` - Reagendar campanha já agendada
- `DELETE /api/campaigns/{id}/unschedule` - Cancelar agendamento
- `GET /api/campaigns/scheduled` - Listar campanhas agendadas
- `GET /api/campaigns/{id}/schedule-history` - Histórico de agendamentos

#### Database:
- **Tabela `campaign_schedule_history`**: Histórico de agendamentos. Campos: id, campaign_id, old_scheduled_at, new_scheduled_at, timezone, reason, user_id, created_at
- **Migração `campaigns`**: Adicionar campos: timezone VARCHAR(50), schedule_notes TEXT, scheduled_by_user_id INT
- **Índices**: (scheduled_at, status), (organization_id, scheduled_at)

#### Domínios:
- **CampaignScheduler**: Gerenciar agendamento e validações de timezone
- **ScheduleHistory**: Rastrear histórico de mudanças de agendamento
- **TimezoneValidator**: Validar e converter timezones

#### Usecases:
- **Campaign/Schedule**: Agendar campanha com validações completas
- **Campaign/Reschedule**: Reagendar campanha existente
- **Campaign/Unschedule**: Cancelar agendamento de campanha
- **Campaign/GetScheduled**: Buscar campanhas agendadas com filtros
- **Campaign/ProcessScheduledCampaigns**: Processar campanhas que chegaram no horário
- **Campaign/ValidateScheduleTime**: Validar se horário de agendamento é válido

#### Jobs/Cron:
- **ProcessScheduledCampaigns**: Executa a cada minuto, processa campanhas que chegaram no horário
- **ValidateScheduledCampaigns**: Executa a cada hora, valida campanhas agendadas para próximas 24h
- **NotifyUpcomingCampaigns**: Executa a cada hora, notifica sobre campanhas que serão enviadas em breve

### 3. Endpoint de Teste em Tempo Real

Esta fase implementa sistema completo de teste de campanhas em tempo real, permitindo validação imediata sem afetar dados de produção.

#### Rotas/Endpoints Necessários:
- `POST /api/campaigns/{id}/test-send` - Enviar teste para número específico
- `GET /api/campaigns/{id}/test-history` - Histórico de testes realizados
- `POST /api/templates/{id}/test-send` - Testar template diretamente
- `GET /api/test-logs/{test_id}` - Detalhes completos de um teste específico

#### Database:
- **Tabela `campaign_test_logs`**: Logs de testes. Campos: id, campaign_id, template_id, test_phone, test_parameters_json, whatsapp_response_json, success, error_message, error_code, processed_message, sent_at, response_time_ms, user_id, created_at
- **Índices**: (campaign_id, created_at), (user_id, created_at), (success, created_at)

#### Domínios:
- **CampaignTester**: Executar testes de campanha em tempo real
- **TestResult**: Encapsular resultado de teste com todos os detalhes
- **TestLogger**: Registrar logs detalhados de testes
- **TemplateProcessor**: Processar templates com parâmetros de teste

#### Usecases:
- **Campaign/TestSend**: Executar teste de envio em tempo real
- **Template/TestSend**: Testar template com parâmetros específicos
- **Campaign/GetTestHistory**: Histórico de testes de uma campanha
- **Test/GetTestDetails**: Detalhes completos de um teste específico
- **Test/AnalyzeTestResults**: Analisar padrões de sucesso/falha em testes
- **Test/GenerateTestReport**: Relatório de testes para uma campanha

#### Jobs/Cron:
- **CleanupTestLogs**: Executa diariamente, remove logs de teste antigos (>7 dias)
- **AnalyzeTestPatterns**: Executa semanalmente, analisa padrões de falha em testes

### 4. Sistema de Logs Avançado e Monitoramento

Esta fase implementa captura avançada de erros WhatsApp, classificação automática, sugestões de correção e dashboard de monitoramento em tempo real.

#### Rotas/Endpoints Necessários:
- `GET /api/campaigns/{id}/error-analysis` - Análise detalhada de erros da campanha
- `GET /api/messages/{id}/error-details` - Detalhes completos de erro de mensagem
- `GET /api/dashboard/campaign-health` - Dashboard de saúde das campanhas
- `GET /api/reports/error-patterns` - Relatório de padrões de erro
- `POST /api/messages/{id}/retry-with-fix` - Retry com correção sugerida

#### Database:
- **Tabela `whatsapp_error_classifications`**: Classificações de erro. Campos: id, error_code, error_pattern, error_type, severity, auto_retry, suggested_fix, description, created_at, updated_at
- **Migração `messages`**: Adicionar campos: whatsapp_error_code VARCHAR(50), whatsapp_error_details JSON, retry_strategy VARCHAR(50), final_failure_reason TEXT, error_classification_id INT, suggested_fix TEXT
- **Tabela `campaign_health_snapshots`**: Snapshots de saúde. Campos: id, campaign_id, total_messages, sent_count, failed_count, error_rate, avg_response_time, health_score, snapshot_at, created_at

#### Domínios:
- **WhatsAppErrorClassifier**: Classificar e categorizar erros do WhatsApp
- **ErrorAnalyzer**: Analisar padrões de erro e gerar insights
- **CampaignHealthMonitor**: Monitorar saúde das campanhas em tempo real
- **ErrorSuggestionEngine**: Gerar sugestões de correção baseadas no tipo de erro
- **HealthScoreCalculator**: Calcular score de saúde das campanhas

#### Usecases:
- **Message/ClassifyError**: Classificar erro de mensagem automaticamente
- **Campaign/AnalyzeErrors**: Analisar todos os erros de uma campanha
- **Campaign/GetHealthScore**: Calcular score de saúde da campanha
- **Error/GenerateSuggestions**: Gerar sugestões de correção para erros
- **Dashboard/GetCampaignHealth**: Dados para dashboard de saúde
- **Report/GenerateErrorPatterns**: Relatório de padrões de erro
- **Message/RetryWithFix**: Retry mensagem aplicando correção sugerida

#### Jobs/Cron:
- **ClassifyUnclassifiedErrors**: Executa a cada 15 minutos, classifica erros novos
- **UpdateCampaignHealthScores**: Executa a cada 30 minutos, atualiza scores de saúde
- **GenerateHealthSnapshots**: Executa a cada hora, cria snapshots de saúde
- **AnalyzeErrorPatterns**: Executa diariamente, analisa padrões de erro
- **SendHealthAlerts**: Executa a cada 5 minutos, envia alertas para campanhas com problemas

## Previsão de PR

Esta seção mapeia todos os arquivos que precisam ser criados ou modificados para implementar completamente esta Epic, organizados por diretório seguindo os padrões do sistema.

### Enums
```
app/Enums/CampaignSyncReason.php (novo)
app/Enums/TestResultStatus.php (novo)
app/Enums/WhatsAppErrorType.php (novo)
app/Enums/CampaignHealthStatus.php (novo)
app/Enums/ErrorSeverity.php (novo)
```

### Models
```
app/Models/CampaignStatusSyncLog.php (novo)
app/Models/CampaignScheduleHistory.php (novo)
app/Models/CampaignTestLog.php (novo)
app/Models/WhatsAppErrorClassification.php (novo)
app/Models/CampaignHealthSnapshot.php (novo)
app/Models/Campaign.php (modificado - novos métodos e relacionamentos)
app/Models/Message.php (modificado - novos campos de erro)
```

### Domains
```
app/Domains/ChatBot/CampaignStatusSyncLog.php (novo)
app/Domains/ChatBot/CampaignStatusAnalyzer.php (novo)
app/Domains/ChatBot/StuckCampaignDetector.php (novo)
app/Domains/ChatBot/CampaignScheduler.php (novo)
app/Domains/ChatBot/ScheduleHistory.php (novo)
app/Domains/ChatBot/TimezoneValidator.php (novo)
app/Domains/ChatBot/CampaignTester.php (novo)
app/Domains/ChatBot/TestResult.php (novo)
app/Domains/ChatBot/TestLogger.php (novo)
app/Domains/ChatBot/TemplateProcessor.php (novo)
app/Domains/ChatBot/WhatsAppErrorClassifier.php (novo)
app/Domains/ChatBot/ErrorAnalyzer.php (novo)
app/Domains/ChatBot/CampaignHealthMonitor.php (novo)
app/Domains/ChatBot/ErrorSuggestionEngine.php (novo)
app/Domains/ChatBot/HealthScoreCalculator.php (novo)
app/Domains/ChatBot/Campaign.php (modificado - novos métodos)
app/Domains/ChatBot/Message.php (modificado - novos métodos de erro)
```

### Factories
```
app/Factories/ChatBot/CampaignStatusSyncLogFactory.php (novo)
app/Factories/ChatBot/CampaignScheduleHistoryFactory.php (novo)
app/Factories/ChatBot/CampaignTestLogFactory.php (novo)
app/Factories/ChatBot/WhatsAppErrorClassificationFactory.php (novo)
app/Factories/ChatBot/CampaignHealthSnapshotFactory.php (novo)
app/Factories/ChatBot/CampaignFactory.php (modificado)
app/Factories/ChatBot/MessageFactory.php (modificado)
```

### Repositories
```
app/Repositories/CampaignStatusSyncLogRepository.php (novo)
app/Repositories/CampaignScheduleHistoryRepository.php (novo)
app/Repositories/CampaignTestLogRepository.php (novo)
app/Repositories/WhatsAppErrorClassificationRepository.php (novo)
app/Repositories/CampaignHealthSnapshotRepository.php (novo)
app/Repositories/CampaignRepository.php (modificado - novos métodos)
app/Repositories/MessageRepository.php (modificado - novos métodos)
```

### Use Cases
```
app/UseCases/ChatBot/Campaign/SyncStatusFromMessages.php (novo)
app/UseCases/ChatBot/Campaign/DetectStuckCampaigns.php (novo)
app/UseCases/ChatBot/Campaign/FixCampaignStatus.php (novo)
app/UseCases/ChatBot/Campaign/GetStatusSyncHistory.php (novo)
app/UseCases/ChatBot/Campaign/AnalyzeMessageStats.php (novo)
app/UseCases/ChatBot/Campaign/Schedule.php (novo)
app/UseCases/ChatBot/Campaign/Reschedule.php (novo)
app/UseCases/ChatBot/Campaign/Unschedule.php (novo)
app/UseCases/ChatBot/Campaign/GetScheduled.php (novo)
app/UseCases/ChatBot/Campaign/ProcessScheduledCampaigns.php (novo)
app/UseCases/ChatBot/Campaign/ValidateScheduleTime.php (novo)
app/UseCases/ChatBot/Campaign/TestSend.php (novo)
app/UseCases/ChatBot/Campaign/GetTestHistory.php (novo)
app/UseCases/ChatBot/Campaign/AnalyzeErrors.php (novo)
app/UseCases/ChatBot/Campaign/GetHealthScore.php (novo)
app/UseCases/ChatBot/Template/TestSend.php (novo)
app/UseCases/ChatBot/Message/ClassifyError.php (novo)
app/UseCases/ChatBot/Message/RetryWithFix.php (novo)
app/UseCases/ChatBot/Test/GetTestDetails.php (novo)
app/UseCases/ChatBot/Test/AnalyzeTestResults.php (novo)
app/UseCases/ChatBot/Test/GenerateTestReport.php (novo)
app/UseCases/ChatBot/Error/GenerateSuggestions.php (novo)
app/UseCases/ChatBot/Dashboard/GetCampaignHealth.php (novo)
app/UseCases/ChatBot/Report/GenerateErrorPatterns.php (novo)
```

### Controllers
```
app/Http/Controllers/ChatBot/CampaignController.php (modificado - novos endpoints)
app/Http/Controllers/ChatBot/MessageController.php (modificado - novos endpoints)
app/Http/Controllers/ChatBot/CampaignTestController.php (novo)
app/Http/Controllers/ChatBot/CampaignHealthController.php (novo)
app/Http/Controllers/ChatBot/ErrorAnalysisController.php (novo)
```

### Requests
```
app/Http/Requests/Campaign/ScheduleRequest.php (novo)
app/Http/Requests/Campaign/RescheduleRequest.php (novo)
app/Http/Requests/Campaign/TestSendRequest.php (novo)
app/Http/Requests/Template/TestSendRequest.php (novo)
app/Http/Requests/Message/RetryWithFixRequest.php (novo)
app/Http/Requests/Dashboard/CampaignHealthRequest.php (novo)
```

### Jobs
```
app/Jobs/SyncCampaignStatusFromMessages.php (novo)
app/Jobs/DetectStuckCampaigns.php (novo)
app/Jobs/CleanupStatusSyncLogs.php (novo)
app/Jobs/ProcessScheduledCampaigns.php (novo)
app/Jobs/ValidateScheduledCampaigns.php (novo)
app/Jobs/NotifyUpcomingCampaigns.php (novo)
app/Jobs/CleanupTestLogs.php (novo)
app/Jobs/AnalyzeTestPatterns.php (novo)
app/Jobs/ClassifyUnclassifiedErrors.php (novo)
app/Jobs/UpdateCampaignHealthScores.php (novo)
app/Jobs/GenerateHealthSnapshots.php (novo)
app/Jobs/AnalyzeErrorPatterns.php (novo)
app/Jobs/SendHealthAlerts.php (novo)
```

### Migrations
```
database/migrations/xxxx_create_campaign_status_sync_logs_table.php (novo)
database/migrations/xxxx_create_campaign_schedule_history_table.php (novo)
database/migrations/xxxx_create_campaign_test_logs_table.php (novo)
database/migrations/xxxx_create_whatsapp_error_classifications_table.php (novo)
database/migrations/xxxx_create_campaign_health_snapshots_table.php (novo)
database/migrations/xxxx_add_status_sync_indexes_to_campaigns_table.php (novo)
database/migrations/xxxx_add_schedule_fields_to_campaigns_table.php (novo)
database/migrations/xxxx_add_error_fields_to_messages_table.php (novo)
database/migrations/xxxx_add_message_indexes_for_performance.php (novo)
```

### Routes
```
routes/api.php (modificado - adicionar todas as novas rotas)
```

### Console
```
app/Console/Kernel.php (modificado - adicionar todos os jobs ao schedule)
```

### Filters
```
app/Domains/Filters/CampaignStatusSyncLogFilters.php (novo)
app/Domains/Filters/CampaignTestLogFilters.php (novo)
app/Domains/Filters/CampaignHealthFilters.php (novo)
app/Domains/Filters/CampaignFilters.php (modificado - novos filtros)
```

### Tests
```
tests/Unit/UseCases/ChatBot/Campaign/SyncStatusFromMessagesTest.php (novo)
tests/Unit/UseCases/ChatBot/Campaign/ScheduleTest.php (novo)
tests/Unit/UseCases/ChatBot/Campaign/TestSendTest.php (novo)
tests/Unit/Domains/ChatBot/CampaignStatusAnalyzerTest.php (novo)
tests/Unit/Domains/ChatBot/WhatsAppErrorClassifierTest.php (novo)
tests/Unit/Jobs/SyncCampaignStatusFromMessagesTest.php (novo)
tests/Unit/Jobs/ProcessScheduledCampaignsTest.php (novo)
tests/Feature/Campaign/StatusSyncTest.php (novo)
tests/Feature/Campaign/SchedulingTest.php (novo)
tests/Feature/Campaign/TestSendTest.php (novo)
tests/Feature/Campaign/ErrorAnalysisTest.php (novo)
```

**Total Estimado: ~95 arquivos (78 novos + 17 modificados)**

## Melhorias Específicas Identificadas

### Problema 1: Status Inconsistente de Campanhas
**Situação Atual**: Campanhas com 100% de mensagens enviadas/entregues mantêm status incorretos como `is_sending=true`, `is_sent=false`, `message_count=0` mesmo com `sent_at` preenchido. O enum `status` não sincroniza com campos boolean legacy, causando dados contraditórios.
**Impacto**: Usuários não conseguem confiar no status exibido, relatórios ficam incorretos, campanhas aparecem como "enviando" indefinidamente, e troubleshooting torna-se impossível. Suporte recebe tickets constantes sobre campanhas "travadas".
**Solução**: Job automático `SyncCampaignStatusFromMessages` que analisa estado real das mensagens a cada 5 minutos e atualiza status da campanha. Job diário `DetectStuckCampaigns` identifica campanhas problemáticas e envia alertas.

### Problema 2: Sistema de Agendamento Não Validado
**Situação Atual**: Existe lógica básica de agendamento (`scheduled_at` no Message), mas não há endpoint para agendar campanhas, validação de timezone, interface de usuário, nem garantia de que funciona corretamente. Usuários não conseguem agendar campanhas de forma confiável.
**Impacto**: Funcionalidade de agendamento inutilizável na prática, usuários precisam enviar campanhas manualmente no horário desejado, perda de oportunidades de marketing em horários ótimos, e impossibilidade de automação de campanhas.
**Solução**: Endpoint `/schedule` com validações completas, job `ProcessScheduledCampaigns` para processar no horário correto, interface para agendamento, e sistema de notificações para campanhas próximas.

### Problema 3: Ausência de Endpoint de Teste
**Situação Atual**: Não existe forma de testar campanhas antes do envio em massa. Usuários precisam criar campanhas reais com um cliente para testar, o que contamina dados de produção e não oferece feedback imediato sobre problemas.
**Impacto**: Campanhas são enviadas com erros para toda a base, templates incorretos chegam aos clientes, debugging é reativo ao invés de preventivo, e qualidade das campanhas fica comprometida.
**Solução**: Endpoint `/test-send` que permite envio para número específico com retorno em tempo real, logs detalhados de erro, processamento de variáveis, sem afetar base de dados de produção.

### Problema 4: Logs de Erro Insuficientes
**Situação Atual**: Apenas campo `last_error_message` com texto genérico, sem códigos específicos do WhatsApp, classificação de tipos de erro, ou sugestões de correção. Debugging de campanhas falhadas é manual e demorado.
**Impacto**: Tempo excessivo para identificar causa de falhas, erros recorrentes não são detectados automaticamente, usuários não recebem orientação sobre como corrigir problemas, e suporte fica sobrecarregado.
**Solução**: Captura detalhada de erros WhatsApp com códigos específicos, classificação automática, sugestões de correção, e dashboard de monitoramento para identificação proativa de problemas.

### Problema 5: Falta de Monitoramento Proativo
**Situação Atual**: Não existe sistema de monitoramento da saúde das campanhas, alertas automáticos para problemas, ou métricas de performance em tempo real. Problemas são descobertos apenas quando usuários reportam.
**Impacto**: Problemas críticos passam despercebidos, campanhas falham silenciosamente, não há visibilidade sobre performance do sistema, e resolução de problemas é sempre reativa.
**Solução**: Dashboard de saúde em tempo real, sistema de alertas automáticos, métricas de performance, e snapshots históricos para análise de tendências.

## Plano de Testes

### Testes Unitários:
- Lógica de determinação de status baseado em estatísticas de mensagens
- Validações de timezone e conversão de horários para agendamento
- Classificação automática de erros WhatsApp por código e padrão
- Cálculo de score de saúde de campanhas baseado em métricas
- Processamento de templates com parâmetros de teste
- Geração de sugestões de correção baseadas no tipo de erro
- Algoritmos de detecção de campanhas "presas"
- Validações de regras de negócio para agendamento
- Transformações de dados entre enum status e campos boolean
- Cálculos de métricas de performance e estatísticas

### Testes de Integração:
- Fluxo completo de sincronização de status: análise de mensagens → determinação de status → atualização de campanha
- Processo de agendamento: criação → validação → processamento no horário → envio
- Endpoint de teste: recebimento de request → processamento → envio WhatsApp → retorno de resultado
- Integração com WhatsApp API para captura de erros específicos
- Jobs de monitoramento: detecção de problemas → geração de alertas → notificação
- Fluxo de classificação de erros: captura → análise → classificação → sugestão
- Processamento de campanhas agendadas com diferentes timezones
- Sistema de retry com correções sugeridas aplicadas

### Testes de Performance:
- Jobs processando 1000+ campanhas simultaneamente sem degradação
- Endpoint de teste respondendo em menos de 5 segundos sob carga
- Queries de sincronização de status otimizadas para grandes volumes
- Sistema de logs suportando alta frequência de erros
- Dashboard carregando métricas em tempo real para centenas de campanhas
- Processamento de campanhas agendadas em horários de pico
- Análise de padrões de erro em bases com milhões de mensagens
- Geração de relatórios complexos sem impacto na performance

### Testes de Regressão:
- Envio normal de campanhas continua funcionando sem alterações
- Sistema de retry existente mantém compatibilidade
- Webhooks de status continuam sendo processados corretamente
- APIs existentes de campanha mantêm mesma interface
- Dados históricos de campanhas permanecem íntegros
- Sistema de templates não é afetado pelas mudanças
- Relacionamentos entre Campaign, Message e Client preservados
- Funcionalidades de filtros e busca continuam operacionais

## Conclusão

Esta Epic transformará o sistema de campanhas WhatsApp de uma solução funcional mas problemática em uma plataforma robusta, confiável e observável. As melhorias abordam problemas críticos que afetam diretamente a experiência do usuário e a confiabilidade operacional, estabelecendo fundações sólidas para futuras evoluções.

### Benefícios Esperados:
- **Confiabilidade**: Status sempre consistentes e atualizados automaticamente, eliminando confusão e tickets de suporte
- **Funcionalidade Completa**: Sistema de agendamento robusto permitindo automação de campanhas em horários ótimos
- **Qualidade**: Endpoint de teste permitindo validação prévia e redução drástica de erros em produção
- **Observabilidade**: Logs detalhados e dashboard de monitoramento facilitando troubleshooting e detecção proativa de problemas
- **Eficiência Operacional**: Redução significativa de trabalho manual e tempo de resolução de problemas
- **Experiência do Usuário**: Interface mais confiável e informativa, com feedback claro sobre status e problemas

### Impacto no Negócio:
- Redução de 80% em tickets de suporte relacionados a status de campanhas
- Aumento de 50% na confiança dos usuários no sistema de agendamento
- Redução de 60% no tempo médio de debugging de campanhas falhadas
- Melhoria de 40% na qualidade das campanhas através de testes prévios
- Redução de 70% no tempo de detecção de problemas críticos
- Aumento de 30% na eficiência operacional da equipe de suporte
- Melhoria significativa na satisfação do usuário e NPS do produto

### Métricas de Sucesso:
- 95% das campanhas com status consistente em tempo real
- 100% das campanhas agendadas executadas no horário correto (±2 minutos)
- Tempo de resposta do endpoint de teste inferior a 3 segundos
- Zero campanhas "presas" por mais de 24 horas
- Redução de 90% em falsos positivos de campanhas problemáticas
- Classificação automática de 85% dos erros WhatsApp
- Dashboard de saúde atualizado em tempo real com latência <30 segundos

## Referências

- WhatsApp Business API Documentation - https://developers.facebook.com/docs/whatsapp
- Laravel Job Scheduling Best Practices - https://laravel.com/docs/scheduling
- Database Indexing for Performance - https://use-the-index-luke.com/
- Error Classification and Monitoring Patterns - https://martinfowler.com/articles/microservice-testing/
- Timezone Handling in PHP Applications - https://www.php.net/manual/en/class.datetimezone.php
- Real-time Dashboard Design Patterns - https://www.nngroup.com/articles/dashboard-design/
- PRD Sistema de Campanhas WhatsApp v2.0 - storage/internal_confluence/PRD-Campanhas-WhatsApp-v2.md
- Epic Campanhas WhatsApp v1 - storage/internal_confluence/schedule/EPICs/ChatBot/campanhas-whatsapp.md
- Documentação Campanhas WhatsApp Atual - storage/internal_confluence/services/Meta/campanhas-whatsapp.md
