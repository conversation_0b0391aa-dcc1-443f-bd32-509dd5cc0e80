# Epic: Sistema de Logs Avançado - Captura Detalhada e Classificação de Erros

## Overview

O sistema atual possui logs básicos de erro através do campo `last_error_message` com texto genérico, sem códigos específicos do WhatsApp, classificação de tipos de erro, ou sugestões de correção. Esta Epic implementa sistema avançado de captura de erros com códigos específicos, classificação automática, sugestões de correção e dashboard de monitoramento para troubleshooting eficiente.

O sistema de logs avançado capturará todos os detalhes das interações com a WhatsApp API, classificará erros automaticamente por tipo e severidade, oferecerá sugestões de correção baseadas no tipo de erro, e fornecerá dashboard em tempo real para monitoramento proativo de problemas.

### Funcionalidades Atuais Identificadas:

- ✅ Campo `last_error_message` no model Message
- ✅ Sistema básico de retry com `delivery_attempts` e `max_retries`
- ✅ Campos `next_retry_at` e `last_attempt_at` para controle temporal
- ✅ DBLog genérico para logging de sistema
- ✅ Enum MessageStatus com estados de erro
- ✅ Captura básica de resposta WhatsApp em alguns casos
- ⚠️ Logs de erro (básicos, sem detalhes específicos)
- ⚠️ Classificação de erros (manual, sem automação)
- ❌ Códigos específicos de erro WhatsApp
- ❌ Classificação automática por tipo de erro
- ❌ Sugestões de correção baseadas no erro
- ❌ Dashboard de monitoramento de erros
- ❌ Análise de padrões de erro
- ❌ Alertas automáticos para erros críticos
- ❌ Relatórios de qualidade de entrega
- ❌ Métricas de performance de envio

### Melhorias Propostas:

#### 1. **Captura Detalhada de Erros WhatsApp**
Implementar captura completa de códigos de erro específicos da WhatsApp API, incluindo headers, payload enviado, resposta completa e contexto do erro.

#### 2. **Sistema de Classificação Automática**
Classificar erros automaticamente por tipo (rate limit, template inválido, número bloqueado, etc.) e severidade (crítico, alto, médio, baixo) para priorização de resolução.

#### 3. **Engine de Sugestões de Correção**
Gerar sugestões automáticas de correção baseadas no tipo de erro, oferecendo orientação específica para resolução de problemas comuns.

#### 4. **Dashboard de Monitoramento em Tempo Real**
Interface para monitoramento de saúde das campanhas, visualização de erros por tipo, métricas de performance e alertas automáticos.

#### 5. **Análise de Padrões e Relatórios**
Sistema para identificar padrões de erro, gerar relatórios de qualidade de entrega e fornecer insights para melhoria contínua.

## Resumo do Plano de Implementação

A Epic será implementada em 3 fases focadas em captura de dados, classificação e análise, seguida por interface e relatórios.

**Fase 1**: Captura Avançada - Implementar captura detalhada de erros com códigos específicos
**Fase 2**: Classificação e Sugestões - Sistema de classificação automática e engine de sugestões
**Fase 3**: Dashboard e Análise - Interface de monitoramento e sistema de relatórios

## Plano de Implementação Detalhado

### 1. Captura Avançada de Erros

Esta fase implementa captura detalhada de todos os aspectos dos erros WhatsApp.

#### Rotas/Endpoints Necessários:
- `GET /api/messages/{id}/error-details` - Detalhes completos de erro de mensagem
- `GET /api/campaigns/{id}/error-summary` - Resumo de erros da campanha
- `POST /api/messages/{id}/retry-with-context` - Retry com contexto de erro

#### Database:
- **Migração `messages`**: Adicionar campos: whatsapp_error_code VARCHAR(50), whatsapp_error_details JSON, error_context JSON, api_request_payload JSON, api_response_headers JSON, error_classification_id INT, suggested_fix TEXT
- **Tabela `whatsapp_error_classifications`**: Classificações de erro. Campos: id, error_code, error_pattern, error_type, severity, auto_retry_recommended, suggested_fix_template, description, frequency_count, last_seen, created_at, updated_at

#### Domínios:
- **WhatsAppErrorCapture**: Capturar erros detalhados da API WhatsApp
- **ErrorDetailExtractor**: Extrair detalhes específicos de respostas de erro
- **ApiResponseLogger**: Registrar payloads e respostas completas
- **ErrorContextBuilder**: Construir contexto completo do erro

#### Usecases:
- **Message/CaptureWhatsAppError**: Capturar erro detalhado da WhatsApp API
- **Message/GetErrorDetails**: Obter detalhes completos de erro
- **Campaign/GetErrorSummary**: Resumo de erros de uma campanha
- **Error/ExtractErrorCode**: Extrair código específico de erro
- **Error/BuildErrorContext**: Construir contexto completo do erro

#### Jobs/Cron:
- **ProcessUnclassifiedErrors**: Executa a cada 15 minutos, processa erros não classificados

### 2. Classificação Automática e Sugestões

Esta fase implementa classificação automática de erros e sistema de sugestões de correção.

#### Rotas/Endpoints Necessários:
- `GET /api/error-classifications` - Lista de classificações de erro
- `GET /api/messages/{id}/suggested-fixes` - Sugestões de correção para mensagem
- `POST /api/error-classifications` - Criar nova classificação (admin)
- `GET /api/campaigns/{id}/error-analysis` - Análise detalhada de erros

#### Database:
- **Tabela `error_suggestion_templates`**: Templates de sugestão. Campos: id, error_type, suggestion_template, priority, success_rate, usage_count, created_at, updated_at
- **Tabela `message_error_suggestions`**: Sugestões aplicadas. Campos: id, message_id, suggestion_id, applied_at, success, user_feedback, created_at

#### Domínios:
- **WhatsAppErrorClassifier**: Classificar erros por tipo e severidade
- **ErrorSuggestionEngine**: Gerar sugestões baseadas no tipo de erro
- **ErrorPatternAnalyzer**: Analisar padrões de erro para classificação
- **SuggestionEffectivenessTracker**: Rastrear efetividade das sugestões

#### Usecases:
- **Error/ClassifyError**: Classificar erro automaticamente
- **Error/GenerateSuggestions**: Gerar sugestões de correção
- **Error/AnalyzePatterns**: Analisar padrões de erro
- **Campaign/AnalyzeErrors**: Análise completa de erros da campanha
- **Suggestion/TrackEffectiveness**: Rastrear efetividade de sugestões
- **Admin/ManageErrorClassifications**: Gerenciar classificações (admin)

#### Jobs/Cron:
- **ClassifyUnclassifiedErrors**: Executa a cada 15 minutos, classifica erros novos
- **UpdateErrorPatterns**: Executa diariamente, atualiza padrões de erro
- **AnalyzeSuggestionEffectiveness**: Executa semanalmente, analisa efetividade

### 3. Dashboard e Sistema de Análise

Esta fase implementa interface de monitoramento e sistema completo de relatórios.

#### Rotas/Endpoints Necessários:
- `GET /api/dashboard/error-overview` - Overview geral de erros
- `GET /api/dashboard/campaign-health` - Dashboard de saúde das campanhas
- `GET /api/reports/error-trends` - Relatório de tendências de erro
- `GET /api/reports/delivery-quality` - Relatório de qualidade de entrega
- `GET /api/alerts/error-alerts` - Alertas de erro ativo

#### Database:
- **Tabela `error_trend_snapshots`**: Snapshots de tendências. Campos: id, organization_id, snapshot_date, total_errors, error_rate, top_error_types JSON, quality_score, created_at
- **Tabela `error_alerts`**: Alertas de erro. Campos: id, organization_id, alert_type, severity, error_pattern, threshold_exceeded, alert_sent, resolved_at, created_at

#### Domínios:
- **ErrorDashboard**: Gerar dados para dashboard de erros
- **CampaignHealthCalculator**: Calcular métricas de saúde das campanhas
- **ErrorTrendAnalyzer**: Analisar tendências de erro ao longo do tempo
- **DeliveryQualityReporter**: Gerar relatórios de qualidade de entrega
- **ErrorAlertManager**: Gerenciar alertas automáticos de erro

#### Usecases:
- **Dashboard/GetErrorOverview**: Overview geral de erros para dashboard
- **Dashboard/GetCampaignHealth**: Métricas de saúde das campanhas
- **Report/GenerateErrorTrends**: Relatório de tendências de erro
- **Report/GenerateDeliveryQuality**: Relatório de qualidade de entrega
- **Alert/ProcessErrorAlerts**: Processar e enviar alertas de erro
- **Analytics/CalculateQualityScore**: Calcular score de qualidade

#### Jobs/Cron:
- **GenerateErrorTrendSnapshots**: Executa diariamente, cria snapshots de tendência
- **ProcessErrorAlerts**: Executa a cada 5 minutos, processa alertas
- **CalculateQualityScores**: Executa a cada hora, calcula scores de qualidade
- **CleanupOldErrorLogs**: Executa semanalmente, limpa logs antigos

## Previsão de PR

### Enums
```
app/Enums/WhatsAppErrorType.php (novo)
app/Enums/ErrorSeverity.php (novo)
app/Enums/ErrorAlertType.php (novo)
app/Enums/SuggestionPriority.php (novo)
```

### Models
```
app/Models/WhatsAppErrorClassification.php (novo)
app/Models/ErrorSuggestionTemplate.php (novo)
app/Models/MessageErrorSuggestion.php (novo)
app/Models/ErrorTrendSnapshot.php (novo)
app/Models/ErrorAlert.php (novo)
app/Models/Message.php (modificado - novos campos de erro)
```

### Domains
```
app/Domains/ChatBot/WhatsAppErrorCapture.php (novo)
app/Domains/ChatBot/ErrorDetailExtractor.php (novo)
app/Domains/ChatBot/ApiResponseLogger.php (novo)
app/Domains/ChatBot/ErrorContextBuilder.php (novo)
app/Domains/ChatBot/WhatsAppErrorClassifier.php (novo)
app/Domains/ChatBot/ErrorSuggestionEngine.php (novo)
app/Domains/ChatBot/ErrorPatternAnalyzer.php (novo)
app/Domains/ChatBot/SuggestionEffectivenessTracker.php (novo)
app/Domains/ChatBot/ErrorDashboard.php (novo)
app/Domains/ChatBot/CampaignHealthCalculator.php (novo)
app/Domains/ChatBot/ErrorTrendAnalyzer.php (novo)
app/Domains/ChatBot/DeliveryQualityReporter.php (novo)
app/Domains/ChatBot/ErrorAlertManager.php (novo)
```

### Factories
```
app/Factories/ChatBot/WhatsAppErrorClassificationFactory.php (novo)
app/Factories/ChatBot/ErrorSuggestionTemplateFactory.php (novo)
app/Factories/ChatBot/MessageErrorSuggestionFactory.php (novo)
app/Factories/ChatBot/ErrorTrendSnapshotFactory.php (novo)
app/Factories/ChatBot/ErrorAlertFactory.php (novo)
```

### Repositories
```
app/Repositories/WhatsAppErrorClassificationRepository.php (novo)
app/Repositories/ErrorSuggestionTemplateRepository.php (novo)
app/Repositories/MessageErrorSuggestionRepository.php (novo)
app/Repositories/ErrorTrendSnapshotRepository.php (novo)
app/Repositories/ErrorAlertRepository.php (novo)
app/Repositories/MessageRepository.php (modificado - métodos de erro)
```

### Use Cases
```
app/UseCases/ChatBot/Message/CaptureWhatsAppError.php (novo)
app/UseCases/ChatBot/Message/GetErrorDetails.php (novo)
app/UseCases/ChatBot/Campaign/GetErrorSummary.php (novo)
app/UseCases/ChatBot/Error/ExtractErrorCode.php (novo)
app/UseCases/ChatBot/Error/BuildErrorContext.php (novo)
app/UseCases/ChatBot/Error/ClassifyError.php (novo)
app/UseCases/ChatBot/Error/GenerateSuggestions.php (novo)
app/UseCases/ChatBot/Error/AnalyzePatterns.php (novo)
app/UseCases/ChatBot/Campaign/AnalyzeErrors.php (novo)
app/UseCases/ChatBot/Suggestion/TrackEffectiveness.php (novo)
app/UseCases/ChatBot/Admin/ManageErrorClassifications.php (novo)
app/UseCases/ChatBot/Dashboard/GetErrorOverview.php (novo)
app/UseCases/ChatBot/Dashboard/GetCampaignHealth.php (novo)
app/UseCases/ChatBot/Report/GenerateErrorTrends.php (novo)
app/UseCases/ChatBot/Report/GenerateDeliveryQuality.php (novo)
app/UseCases/ChatBot/Alert/ProcessErrorAlerts.php (novo)
app/UseCases/ChatBot/Analytics/CalculateQualityScore.php (novo)
```

### Controllers
```
app/Http/Controllers/ChatBot/MessageController.php (modificado - endpoints de erro)
app/Http/Controllers/ChatBot/ErrorAnalysisController.php (novo)
app/Http/Controllers/ChatBot/ErrorDashboardController.php (novo)
app/Http/Controllers/ChatBot/ErrorReportController.php (novo)
app/Http/Controllers/Admin/ErrorClassificationController.php (novo)
```

### Jobs
```
app/Jobs/ProcessUnclassifiedErrors.php (novo)
app/Jobs/ClassifyUnclassifiedErrors.php (novo)
app/Jobs/UpdateErrorPatterns.php (novo)
app/Jobs/AnalyzeSuggestionEffectiveness.php (novo)
app/Jobs/GenerateErrorTrendSnapshots.php (novo)
app/Jobs/ProcessErrorAlerts.php (novo)
app/Jobs/CalculateQualityScores.php (novo)
app/Jobs/CleanupOldErrorLogs.php (novo)
```

**Total Estimado: ~50 arquivos (48 novos + 2 modificados)**

## Melhorias Específicas Identificadas

### Problema 1: Logs de Erro Insuficientes para Debugging
**Situação Atual**: Apenas campo `last_error_message` com texto genérico, sem códigos específicos do WhatsApp, detalhes da requisição, ou contexto do erro. Debugging de campanhas falhadas é manual e demorado.
**Impacto**: Tempo excessivo para identificar causa de falhas, impossibilidade de debugging automático, erros recorrentes não são detectados, e resolução de problemas é sempre reativa.
**Solução**: Captura detalhada de códigos específicos WhatsApp, payload completo, headers, contexto do erro e classificação automática para debugging eficiente.

### Problema 2: Ausência de Classificação e Priorização de Erros
**Situação Atual**: Todos os erros são tratados igualmente, sem classificação por tipo (rate limit, template inválido, número bloqueado) ou severidade, dificultando priorização de resolução.
**Impacto**: Problemas críticos podem passar despercebidos, recursos desperdiçados em erros de baixa prioridade, e falta de estratégia para resolução sistemática de problemas.
**Solução**: Sistema de classificação automática por tipo e severidade, com priorização inteligente e sugestões específicas de correção para cada categoria.

### Problema 3: Falta de Visibilidade e Monitoramento Proativo
**Situação Atual**: Não existe dashboard ou sistema de monitoramento para visualizar saúde das campanhas, padrões de erro, ou métricas de qualidade de entrega em tempo real.
**Impacto**: Problemas são descobertos apenas quando usuários reportam, não há visibilidade sobre performance geral, e gestão de qualidade é impossível sem métricas.
**Solução**: Dashboard em tempo real com métricas de saúde, alertas automáticos para problemas críticos, e relatórios de tendências para melhoria contínua.

## Plano de Testes

### Testes Unitários:
- Captura e extração de códigos específicos de erro WhatsApp
- Classificação automática de erros por tipo e severidade
- Geração de sugestões de correção baseadas no tipo de erro
- Cálculo de métricas de qualidade e scores de saúde
- Análise de padrões de erro e detecção de tendências
- Validação de templates de sugestão e efetividade
- Algoritmos de priorização de alertas por severidade

### Testes de Integração:
- Fluxo completo de captura: erro WhatsApp → classificação → sugestão → dashboard
- Sistema de alertas detectando e notificando problemas críticos
- Dashboard exibindo métricas em tempo real com dados precisos
- Relatórios de qualidade gerados com dados históricos corretos
- Jobs de análise processando grandes volumes de erros
- Interface de administração para gerenciar classificações

### Testes de Performance:
- Captura de erros sem impacto na performance de envio
- Classificação automática processando 1000+ erros por minuto
- Dashboard carregando métricas rapidamente mesmo com histórico extenso
- Queries de análise otimizadas para grandes volumes de dados
- Sistema de alertas com latência mínima para problemas críticos

### Testes de Regressão:
- Sistema de envio de mensagens não é afetado pela captura de erros
- Funcionalidades existentes de retry mantêm compatibilidade
- APIs de campanha existentes continuam funcionando normalmente
- Dados históricos de mensagens permanecem íntegros
- Performance geral do sistema não é degradada

## Conclusão

Esta Epic transforma o sistema de logs de uma funcionalidade básica em uma plataforma avançada de observabilidade e debugging. A implementação de captura detalhada, classificação automática e dashboard de monitoramento permitirá identificação proativa de problemas e resolução eficiente de falhas.

### Benefícios Esperados:
- **Observabilidade**: Visibilidade completa sobre saúde e performance das campanhas
- **Eficiência**: Debugging rápido com logs detalhados e sugestões automáticas
- **Proatividade**: Detecção automática de problemas antes de afetar usuários
- **Qualidade**: Melhoria contínua através de análise de padrões e métricas
- **Automação**: Classificação e sugestões automáticas reduzindo trabalho manual
- **Inteligência**: Insights sobre padrões de erro para otimização do sistema

### Impacto no Negócio:
- Redução de 80% no tempo de debugging de campanhas falhadas
- Melhoria de 70% na detecção proativa de problemas críticos
- Aumento de 60% na qualidade geral das campanhas
- Redução de 50% em tickets de suporte relacionados a erros
- Melhoria de 90% na precisão de diagnóstico de problemas
- Aumento significativo na confiabilidade percebida do sistema

### Métricas de Sucesso:
- 95% dos erros WhatsApp classificados automaticamente
- Tempo médio de debugging reduzido para <15 minutos
- Dashboard atualizado em tempo real com latência <30 segundos
- 90% dos problemas críticos detectados automaticamente
- Sugestões de correção com 80% de efetividade
- Alertas automáticos com <5% de falsos positivos

## Referências

- WhatsApp Business API Error Codes - https://developers.facebook.com/docs/whatsapp/api/errors
- Error Classification and Monitoring Patterns - https://martinfowler.com/articles/microservice-testing/
- Real-time Dashboard Design - https://www.nngroup.com/articles/dashboard-design/
- Log Analysis and Pattern Recognition - https://www.elastic.co/guide/en/elasticsearch/reference/current/analysis.html
- Epic Campanhas WhatsApp v2.0 (documento base) - storage/internal_confluence/schedule/EPICs/ChatBot/campanhas-whatsapp-v2.md
- Documentação WhatsApp API Errors - storage/internal_confluence/services/Meta/whatsapp-errors.md
