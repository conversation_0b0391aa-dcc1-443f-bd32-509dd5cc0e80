# Epic: Monitoramento e Alertas Automáticos - Observabilidade Proativa do Sistema

## Overview

Atualmente não existe sistema de monitoramento proativo da saúde das campanhas, alertas automáticos para problemas críticos, ou métricas de performance em tempo real. Problemas são descobertos apenas quando usuários reportam, resultando em resolução sempre reativa. Esta Epic implementa sistema completo de monitoramento com alertas automáticos, métricas de performance e dashboard de observabilidade para gestão proativa do sistema.

O sistema de monitoramento fornecerá visibilidade em tempo real sobre saúde das campanhas, performance de envio, padrões de falha e tendências de uso, com alertas automáticos para problemas críticos e dashboard executivo para tomada de decisão baseada em dados.

### Funcionalidades Atuais Identificadas:

- ✅ Logs básicos através do DBLog para eventos do sistema
- ✅ Métricas básicas de campanha (message_count, status)
- ✅ Sistema de status de mensagens e campanhas
- ✅ Jobs executando em intervalos regulares
- ✅ Dados de performance básicos (delivery_attempts, response_time)
- ⚠️ Monitoramento de saúde (manual, sem automação)
- ⚠️ Métricas de performance (básicas, sem agregação)
- ❌ Dashboard de observabilidade em tempo real
- ❌ Alertas automáticos para problemas críticos
- ❌ Métricas agregadas de performance do sistema
- ❌ Monitoramento de SLA e uptime
- ❌ Análise de tendências e previsões
- ❌ Alertas de capacidade e recursos
- ❌ Relatórios executivos automatizados
- ❌ Sistema de notificações multi-canal

### Melhorias Propostas:

#### 1. **Dashboard de Observabilidade em Tempo Real**
Implementar dashboard executivo com métricas de saúde do sistema, performance de campanhas, status de jobs, e indicadores de qualidade de serviço em tempo real.

#### 2. **Sistema de Alertas Inteligentes**
Criar sistema de alertas automáticos baseado em thresholds configuráveis, detecção de anomalias e padrões de falha, com notificações multi-canal (email, Slack, webhook).

#### 3. **Métricas de Performance e SLA**
Implementar coleta e agregação de métricas de performance, cálculo de SLA, uptime, e indicadores de qualidade de serviço com histórico e tendências.

#### 4. **Monitoramento de Capacidade e Recursos**
Monitorar utilização de recursos, capacidade de processamento, filas de jobs e performance de banco de dados com alertas preventivos.

#### 5. **Relatórios Executivos Automatizados**
Gerar relatórios automáticos de performance, qualidade e saúde do sistema para stakeholders com insights e recomendações.

## Resumo do Plano de Implementação

A Epic será implementada em 3 fases focadas em coleta de dados, alertas e dashboard, seguida por análise avançada e relatórios.

**Fase 1**: Coleta de Métricas - Implementar coleta sistemática de métricas e indicadores de saúde
**Fase 2**: Alertas e Notificações - Sistema de alertas automáticos com notificações multi-canal
**Fase 3**: Dashboard e Relatórios - Interface de observabilidade e relatórios executivos

## Plano de Implementação Detalhado

### 1. Coleta Sistemática de Métricas

Esta fase implementa coleta abrangente de métricas de performance e saúde do sistema.

#### Rotas/Endpoints Necessários:
- `GET /api/monitoring/health` - Health check geral do sistema
- `GET /api/monitoring/metrics` - Métricas em tempo real
- `GET /api/monitoring/performance` - Indicadores de performance
- `GET /api/monitoring/sla` - Métricas de SLA e uptime

#### Database:
- **Tabela `system_health_metrics`**: Métricas de saúde. Campos: id, metric_name, metric_value, metric_unit, category, organization_id, recorded_at, created_at
- **Tabela `performance_snapshots`**: Snapshots de performance. Campos: id, snapshot_type, campaigns_active, messages_pending, messages_sent_last_hour, error_rate, avg_response_time, system_load, recorded_at, created_at
- **Tabela `sla_metrics`**: Métricas de SLA. Campos: id, service_name, uptime_percentage, availability_minutes, downtime_minutes, incidents_count, mttr_minutes, period_start, period_end, created_at

#### Domínios:
- **SystemHealthCollector**: Coletar métricas de saúde do sistema
- **PerformanceMetricsCalculator**: Calcular métricas de performance
- **SLACalculator**: Calcular métricas de SLA e uptime
- **MetricsAggregator**: Agregar métricas por período e categoria

#### Usecases:
- **Monitoring/CollectHealthMetrics**: Coletar métricas de saúde do sistema
- **Monitoring/CalculatePerformance**: Calcular indicadores de performance
- **Monitoring/CalculateSLA**: Calcular métricas de SLA
- **Monitoring/GetSystemHealth**: Obter status geral de saúde
- **Monitoring/GetPerformanceMetrics**: Obter métricas de performance
- **Metrics/AggregateByPeriod**: Agregar métricas por período

#### Jobs/Cron:
- **CollectSystemHealthMetrics**: Executa a cada 5 minutos, coleta métricas de saúde
- **CalculatePerformanceSnapshots**: Executa a cada 15 minutos, cria snapshots de performance
- **CalculateSLAMetrics**: Executa a cada hora, calcula métricas de SLA

### 2. Sistema de Alertas e Notificações

Esta fase implementa sistema completo de alertas automáticos com notificações multi-canal.

#### Rotas/Endpoints Necessários:
- `GET /api/alerts/active` - Alertas ativos no sistema
- `GET /api/alerts/history` - Histórico de alertas
- `POST /api/alerts/acknowledge` - Reconhecer alerta
- `GET /api/alerts/configuration` - Configuração de alertas
- `PUT /api/alerts/configuration` - Atualizar configuração de alertas

#### Database:
- **Tabela `alert_rules`**: Regras de alerta. Campos: id, rule_name, metric_name, threshold_value, comparison_operator, severity, enabled, notification_channels JSON, cooldown_minutes, created_at, updated_at
- **Tabela `alert_incidents`**: Incidentes de alerta. Campos: id, rule_id, triggered_at, resolved_at, severity, metric_value, threshold_exceeded, acknowledged_by, acknowledgment_note, notification_sent, created_at
- **Tabela `notification_channels`**: Canais de notificação. Campos: id, organization_id, channel_type, channel_config JSON, enabled, created_at, updated_at

#### Domínios:
- **AlertRuleEngine**: Avaliar regras de alerta contra métricas
- **AlertNotificationManager**: Gerenciar envio de notificações
- **IncidentManager**: Gerenciar ciclo de vida de incidentes
- **ThresholdEvaluator**: Avaliar thresholds e condições de alerta

#### Usecases:
- **Alert/EvaluateRules**: Avaliar regras de alerta contra métricas atuais
- **Alert/TriggerIncident**: Disparar incidente quando threshold é excedido
- **Alert/SendNotification**: Enviar notificação através de canais configurados
- **Alert/AcknowledgeIncident**: Reconhecer incidente ativo
- **Alert/ResolveIncident**: Resolver incidente automaticamente
- **Alert/ManageConfiguration**: Gerenciar configuração de alertas

#### Jobs/Cron:
- **EvaluateAlertRules**: Executa a cada minuto, avalia regras de alerta
- **ProcessAlertNotifications**: Executa a cada 2 minutos, processa notificações pendentes
- **ResolveStaleIncidents**: Executa a cada 15 minutos, resolve incidentes antigos

### 3. Dashboard de Observabilidade e Relatórios

Esta fase implementa interface completa de observabilidade e sistema de relatórios executivos.

#### Rotas/Endpoints Necessários:
- `GET /api/dashboard/executive` - Dashboard executivo com KPIs principais
- `GET /api/dashboard/operations` - Dashboard operacional detalhado
- `GET /api/dashboard/real-time` - Métricas em tempo real
- `GET /api/reports/weekly-summary` - Relatório semanal automatizado
- `GET /api/reports/monthly-executive` - Relatório executivo mensal

#### Database:
- **Tabela `dashboard_widgets`**: Configuração de widgets. Campos: id, widget_type, widget_config JSON, position, size, user_id, organization_id, created_at, updated_at
- **Tabela `automated_reports`**: Relatórios automatizados. Campos: id, report_type, recipients JSON, schedule_config JSON, last_sent, next_scheduled, enabled, created_at, updated_at

#### Domínios:
- **ExecutiveDashboard**: Gerar dados para dashboard executivo
- **OperationalDashboard**: Gerar dados para dashboard operacional
- **RealTimeMetricsProvider**: Fornecer métricas em tempo real
- **AutomatedReportGenerator**: Gerar relatórios automatizados
- **DashboardWidgetManager**: Gerenciar widgets personalizáveis

#### Usecases:
- **Dashboard/GetExecutiveData**: Dados para dashboard executivo
- **Dashboard/GetOperationalData**: Dados para dashboard operacional
- **Dashboard/GetRealTimeMetrics**: Métricas em tempo real
- **Report/GenerateWeeklySummary**: Gerar relatório semanal
- **Report/GenerateMonthlyExecutive**: Gerar relatório executivo mensal
- **Dashboard/ManageWidgets**: Gerenciar widgets do dashboard

#### Jobs/Cron:
- **GenerateWeeklyReports**: Executa semanalmente, gera relatórios automáticos
- **GenerateMonthlyReports**: Executa mensalmente, gera relatórios executivos
- **UpdateDashboardCache**: Executa a cada 5 minutos, atualiza cache do dashboard
- **CleanupOldMetrics**: Executa diariamente, limpa métricas antigas

## Previsão de PR

### Enums
```
app/Enums/MetricCategory.php (novo)
app/Enums/AlertSeverity.php (novo)
app/Enums/NotificationChannel.php (novo)
app/Enums/IncidentStatus.php (novo)
app/Enums/ReportType.php (novo)
```

### Models
```
app/Models/SystemHealthMetric.php (novo)
app/Models/PerformanceSnapshot.php (novo)
app/Models/SLAMetric.php (novo)
app/Models/AlertRule.php (novo)
app/Models/AlertIncident.php (novo)
app/Models/NotificationChannel.php (novo)
app/Models/DashboardWidget.php (novo)
app/Models/AutomatedReport.php (novo)
```

### Domains
```
app/Domains/Monitoring/SystemHealthCollector.php (novo)
app/Domains/Monitoring/PerformanceMetricsCalculator.php (novo)
app/Domains/Monitoring/SLACalculator.php (novo)
app/Domains/Monitoring/MetricsAggregator.php (novo)
app/Domains/Monitoring/AlertRuleEngine.php (novo)
app/Domains/Monitoring/AlertNotificationManager.php (novo)
app/Domains/Monitoring/IncidentManager.php (novo)
app/Domains/Monitoring/ThresholdEvaluator.php (novo)
app/Domains/Monitoring/ExecutiveDashboard.php (novo)
app/Domains/Monitoring/OperationalDashboard.php (novo)
app/Domains/Monitoring/RealTimeMetricsProvider.php (novo)
app/Domains/Monitoring/AutomatedReportGenerator.php (novo)
app/Domains/Monitoring/DashboardWidgetManager.php (novo)
```

### Factories
```
app/Factories/Monitoring/SystemHealthMetricFactory.php (novo)
app/Factories/Monitoring/PerformanceSnapshotFactory.php (novo)
app/Factories/Monitoring/SLAMetricFactory.php (novo)
app/Factories/Monitoring/AlertRuleFactory.php (novo)
app/Factories/Monitoring/AlertIncidentFactory.php (novo)
app/Factories/Monitoring/NotificationChannelFactory.php (novo)
app/Factories/Monitoring/DashboardWidgetFactory.php (novo)
app/Factories/Monitoring/AutomatedReportFactory.php (novo)
```

### Repositories
```
app/Repositories/SystemHealthMetricRepository.php (novo)
app/Repositories/PerformanceSnapshotRepository.php (novo)
app/Repositories/SLAMetricRepository.php (novo)
app/Repositories/AlertRuleRepository.php (novo)
app/Repositories/AlertIncidentRepository.php (novo)
app/Repositories/NotificationChannelRepository.php (novo)
app/Repositories/DashboardWidgetRepository.php (novo)
app/Repositories/AutomatedReportRepository.php (novo)
```

### Use Cases
```
app/UseCases/Monitoring/CollectHealthMetrics.php (novo)
app/UseCases/Monitoring/CalculatePerformance.php (novo)
app/UseCases/Monitoring/CalculateSLA.php (novo)
app/UseCases/Monitoring/GetSystemHealth.php (novo)
app/UseCases/Monitoring/GetPerformanceMetrics.php (novo)
app/UseCases/Metrics/AggregateByPeriod.php (novo)
app/UseCases/Alert/EvaluateRules.php (novo)
app/UseCases/Alert/TriggerIncident.php (novo)
app/UseCases/Alert/SendNotification.php (novo)
app/UseCases/Alert/AcknowledgeIncident.php (novo)
app/UseCases/Alert/ResolveIncident.php (novo)
app/UseCases/Alert/ManageConfiguration.php (novo)
app/UseCases/Dashboard/GetExecutiveData.php (novo)
app/UseCases/Dashboard/GetOperationalData.php (novo)
app/UseCases/Dashboard/GetRealTimeMetrics.php (novo)
app/UseCases/Dashboard/ManageWidgets.php (novo)
app/UseCases/Report/GenerateWeeklySummary.php (novo)
app/UseCases/Report/GenerateMonthlyExecutive.php (novo)
```

### Controllers
```
app/Http/Controllers/Monitoring/SystemHealthController.php (novo)
app/Http/Controllers/Monitoring/AlertController.php (novo)
app/Http/Controllers/Monitoring/DashboardController.php (novo)
app/Http/Controllers/Monitoring/ReportController.php (novo)
```

### Jobs
```
app/Jobs/CollectSystemHealthMetrics.php (novo)
app/Jobs/CalculatePerformanceSnapshots.php (novo)
app/Jobs/CalculateSLAMetrics.php (novo)
app/Jobs/EvaluateAlertRules.php (novo)
app/Jobs/ProcessAlertNotifications.php (novo)
app/Jobs/ResolveStaleIncidents.php (novo)
app/Jobs/GenerateWeeklyReports.php (novo)
app/Jobs/GenerateMonthlyReports.php (novo)
app/Jobs/UpdateDashboardCache.php (novo)
app/Jobs/CleanupOldMetrics.php (novo)
```

**Total Estimado: ~55 arquivos (55 novos + 0 modificados)**

## Melhorias Específicas Identificadas

### Problema 1: Ausência de Monitoramento Proativo
**Situação Atual**: Não existe sistema de monitoramento da saúde das campanhas, métricas de performance em tempo real, ou visibilidade sobre status geral do sistema. Problemas são descobertos apenas quando usuários reportam.
**Impacto**: Problemas críticos passam despercebidos, campanhas falham silenciosamente, não há visibilidade sobre performance do sistema, e resolução de problemas é sempre reativa ao invés de preventiva.
**Solução**: Dashboard de observabilidade em tempo real com métricas de saúde, performance e qualidade, permitindo monitoramento proativo e detecção precoce de problemas.

### Problema 2: Falta de Alertas Automáticos para Problemas Críticos
**Situação Atual**: Não existem alertas automáticos quando thresholds críticos são excedidos, taxa de erro aumenta significativamente, ou sistema apresenta degradação de performance.
**Impacto**: Problemas críticos podem afetar muitos usuários antes de serem detectados, perda de SLA, impacto na experiência do usuário, e danos à reputação do sistema.
**Solução**: Sistema de alertas inteligentes com thresholds configuráveis, detecção de anomalias e notificações multi-canal para resposta rápida a incidentes.

### Problema 3: Ausência de Métricas de SLA e Performance
**Situação Atual**: Não há coleta sistemática de métricas de uptime, SLA, performance de envio, ou indicadores de qualidade de serviço, impossibilitando gestão baseada em dados.
**Impacto**: Impossibilidade de medir qualidade do serviço, falta de dados para tomada de decisão, ausência de accountability sobre performance, e dificuldade para identificar áreas de melhoria.
**Solução**: Coleta abrangente de métricas de SLA, uptime, performance e qualidade com histórico e tendências para gestão data-driven.

### Problema 4: Falta de Relatórios Executivos e Insights
**Situação Atual**: Não existem relatórios automatizados sobre saúde do sistema, performance das campanhas, ou insights para stakeholders e tomada de decisão executiva.
**Impacto**: Falta de visibilidade executiva sobre performance do sistema, decisões baseadas em percepção ao invés de dados, e ausência de insights para melhoria estratégica.
**Solução**: Relatórios executivos automatizados com KPIs, tendências, insights e recomendações para stakeholders e tomada de decisão estratégica.

## Plano de Testes

### Testes Unitários:
- Coleta e agregação de métricas de saúde do sistema
- Cálculo de SLA, uptime e indicadores de performance
- Avaliação de regras de alerta contra thresholds configurados
- Geração de notificações através de diferentes canais
- Cálculo de KPIs e métricas para dashboards
- Geração de relatórios automatizados com dados precisos
- Algoritmos de detecção de anomalias e padrões

### Testes de Integração:
- Fluxo completo de monitoramento: coleta → agregação → avaliação → alerta
- Sistema de alertas detectando problemas e enviando notificações
- Dashboard exibindo métricas em tempo real com dados atualizados
- Relatórios automatizados sendo gerados e enviados corretamente
- Jobs de coleta de métricas executando sem impacto na performance
- Integração com canais de notificação (email, Slack, webhook)

### Testes de Performance:
- Coleta de métricas sem impacto na performance do sistema principal
- Dashboard carregando rapidamente mesmo com histórico extenso
- Sistema de alertas com latência mínima para problemas críticos
- Queries de agregação otimizadas para grandes volumes de dados
- Geração de relatórios sem degradação de performance

### Testes de Regressão:
- Sistema de campanhas não é afetado pela coleta de métricas
- Performance de envio de mensagens mantém-se inalterada
- APIs existentes continuam funcionando normalmente
- Dados históricos permanecem íntegros após implementação
- Funcionalidades existentes não são impactadas pelo monitoramento

## Conclusão

Esta Epic estabelece fundação sólida de observabilidade para todo o sistema de campanhas WhatsApp, transformando gestão reativa em proativa através de monitoramento em tempo real, alertas automáticos e relatórios executivos. A implementação permitirá gestão data-driven e melhoria contínua da qualidade de serviço.

### Benefícios Esperados:
- **Proatividade**: Detecção automática de problemas antes de afetar usuários
- **Visibilidade**: Dashboard executivo com KPIs e métricas em tempo real
- **Responsividade**: Alertas automáticos para resposta rápida a incidentes
- **Qualidade**: Monitoramento de SLA e indicadores de qualidade de serviço
- **Insights**: Relatórios automatizados com análises e recomendações
- **Confiabilidade**: Sistema auto-monitorado com alta disponibilidade

### Impacto no Negócio:
- Redução de 90% no tempo de detecção de problemas críticos
- Melhoria de 80% na disponibilidade e uptime do sistema
- Aumento de 70% na confiança dos stakeholders através de transparência
- Redução de 60% em incidentes não detectados proativamente
- Melhoria de 50% na tomada de decisão através de dados precisos
- Aumento significativo na satisfação do usuário por maior estabilidade

### Métricas de Sucesso:
- 99.9% de uptime do sistema de monitoramento
- Detecção de 95% dos problemas críticos em <2 minutos
- Dashboard atualizado em tempo real com latência <15 segundos
- Alertas enviados em <30 segundos após detecção
- Relatórios executivos gerados automaticamente sem falhas
- SLA de campanhas >99.5% com monitoramento contínuo

## Referências

- Observability Best Practices - https://sre.google/sre-book/monitoring-distributed-systems/
- Alert Fatigue and Intelligent Alerting - https://docs.datadoghq.com/monitors/guide/reduce-alert-fatigue/
- Dashboard Design for Operations - https://grafana.com/blog/2017/01/05/dashboard-design-best-practices/
- SLA and SLO Management - https://sre.google/sre-book/service-level-objectives/
- Real-time Metrics Collection - https://prometheus.io/docs/practices/naming/
- Epic Campanhas WhatsApp v2.0 (documento base) - storage/internal_confluence/schedule/EPICs/ChatBot/campanhas-whatsapp-v2.md
- Documentação Sistema de Logs - storage/internal_confluence/services/Meta/system-logs.md
