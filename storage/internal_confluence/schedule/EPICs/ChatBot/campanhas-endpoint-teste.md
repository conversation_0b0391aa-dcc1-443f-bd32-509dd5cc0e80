# Epic: Endpoint de Teste em Tempo Real - Validação de Campanhas Antes do Envio

## Overview

Atualmente não existe forma de testar campanhas WhatsApp antes do envio em massa, forçando usuários a criar campanhas reais com clientes para validação, contaminando dados de produção e não oferecendo feedback imediato sobre problemas. Esta Epic implementa sistema completo de teste em tempo real que permite validar campanhas com número específico, retornando resultado imediato com logs detalhados.

O endpoint de teste processará templates com parâmetros customizados, enviará via WhatsApp API, capturará resposta completa e retornará resultado em tempo real sem afetar base de dados de produção, permitindo debugging preventivo ao invés de reativo.

### Funcionalidades Atuais Identificadas:

- ✅ Sistema de templates com variáveis dinâmicas ({{client.name}}, etc.)
- ✅ Processamento de parâmetros de campanha
- ✅ Integração com WhatsApp Business API para envio
- ✅ Sistema de logs básico com DBLog
- ✅ Validação de templates e parâmetros
- ✅ Geração de mensagens com substituição de variáveis
- ❌ Endpoint para teste de campanhas específicas
- ❌ Teste de templates isoladamente
- ❌ Processamento de parâmetros customizados para teste
- ❌ Logs detalhados de teste em tempo real
- ❌ Histórico de testes realizados
- ❌ Interface para visualizar resultados de teste
- ❌ Validação de números de telefone para teste
- ❌ Sistema de rate limiting para testes

### Melhorias Propostas:

#### 1. **Endpoint de Teste de Campanha**
Implementar endpoint `/test-send` que permite testar campanha específica com número de telefone customizado e parâmetros de teste, retornando resultado em tempo real com logs detalhados.

#### 2. **Teste de Template Isolado**
Permitir teste de templates diretamente sem necessidade de criar campanha, facilitando validação durante desenvolvimento de templates.

#### 3. **Sistema de Logs de Teste Detalhado**
Capturar todos os detalhes do processo de teste: processamento de template, payload enviado, resposta WhatsApp, tempo de execução, e classificação de erros.

#### 4. **Histórico e Análise de Testes**
Manter histórico de testes realizados para análise de padrões, debugging e melhoria contínua da qualidade das campanhas.

#### 5. **Rate Limiting e Validações**
Implementar controles para evitar abuso do sistema de teste, validações de número de telefone e limitações por organização.

## Resumo do Plano de Implementação

A Epic será implementada em 3 fases focadas em funcionalidade core primeiro, seguida por melhorias e otimizações.

**Fase 1**: Endpoint Core - Implementar endpoint básico de teste com funcionalidade essencial
**Fase 2**: Logs e Histórico - Adicionar sistema completo de logs e histórico de testes
**Fase 3**: Otimizações - Implementar rate limiting, validações avançadas e interface de usuário

## Plano de Implementação Detalhado

### 1. Implementação do Endpoint Core

Esta fase implementa a funcionalidade essencial de teste de campanhas e templates em tempo real.

#### Rotas/Endpoints Necessários:
- `POST /api/campaigns/{id}/test-send` - Testar campanha com número específico
- `POST /api/templates/{id}/test-send` - Testar template diretamente
- `GET /api/campaigns/{id}/test-validation` - Validar se campanha pode ser testada

#### Database:
- **Tabela `campaign_test_logs`**: Logs de teste. Campos: id, campaign_id, template_id, test_phone, test_parameters_json, whatsapp_response_json, success, error_message, error_code, processed_message, sent_at, response_time_ms, user_id, organization_id, created_at

#### Domínios:
- **CampaignTester**: Executar testes de campanha com validações completas
- **TemplateTester**: Testar templates isoladamente com parâmetros customizados
- **TestResult**: Encapsular resultado de teste com todos os detalhes
- **TestParameterProcessor**: Processar parâmetros de teste e substituir variáveis

#### Usecases:
- **Campaign/TestSend**: Executar teste de campanha em tempo real
- **Template/TestSend**: Testar template com parâmetros específicos
- **Campaign/ValidateForTest**: Validar se campanha pode ser testada
- **Test/ProcessParameters**: Processar parâmetros de teste
- **Test/SendToWhatsApp**: Enviar teste via WhatsApp API

#### Jobs/Cron:
- **CleanupTestLogs**: Executa diariamente, remove logs de teste >7 dias

### 2. Sistema de Logs e Histórico Completo

Esta fase implementa captura detalhada de logs de teste e sistema de histórico para análise.

#### Rotas/Endpoints Necessários:
- `GET /api/campaigns/{id}/test-history` - Histórico de testes da campanha
- `GET /api/templates/{id}/test-history` - Histórico de testes do template
- `GET /api/test-logs/{test_id}` - Detalhes completos de teste específico
- `GET /api/organization/test-summary` - Resumo de testes da organização

#### Database:
- **Migração `campaign_test_logs`**: Adicionar índices: (campaign_id, created_at), (template_id, created_at), (organization_id, success, created_at)
- **Tabela `test_error_patterns`**: Padrões de erro em testes. Campos: id, error_code, error_pattern, frequency, last_seen, suggested_fix, created_at, updated_at

#### Domínios:
- **TestLogger**: Registrar logs detalhados com classificação automática
- **TestHistoryAnalyzer**: Analisar histórico de testes para insights
- **TestErrorClassifier**: Classificar erros de teste automaticamente
- **TestReportGenerator**: Gerar relatórios de testes

#### Usecases:
- **Test/GetTestHistory**: Buscar histórico de testes com filtros
- **Test/GetTestDetails**: Detalhes completos de teste específico
- **Test/AnalyzeTestResults**: Analisar padrões de sucesso/falha
- **Test/GenerateTestReport**: Relatório de testes para campanha/template
- **Test/GetOrganizationSummary**: Resumo de testes da organização

#### Jobs/Cron:
- **AnalyzeTestPatterns**: Executa semanalmente, analisa padrões de teste

### 3. Rate Limiting e Validações Avançadas

Esta fase implementa controles de segurança, validações avançadas e interface de usuário.

#### Rotas/Endpoints Necessários:
- `GET /api/organization/test-limits` - Verificar limites de teste disponíveis
- `POST /api/test/validate-phone` - Validar número de telefone para teste
- `GET /api/test/dashboard` - Dashboard de testes da organização

#### Database:
- **Tabela `test_rate_limits`**: Controle de rate limiting. Campos: id, organization_id, user_id, tests_count, period_start, period_end, limit_exceeded_at, created_at, updated_at
- **Migração `organizations`**: Adicionar campos: daily_test_limit INT DEFAULT 50, monthly_test_limit INT DEFAULT 500

#### Domínios:
- **TestRateLimiter**: Controlar limites de teste por organização/usuário
- **PhoneValidator**: Validar números de telefone para teste
- **TestDashboard**: Gerar dados para dashboard de testes
- **TestQuotaManager**: Gerenciar cotas de teste

#### Usecases:
- **Test/CheckRateLimit**: Verificar se pode executar teste
- **Test/ValidatePhoneNumber**: Validar número para teste
- **Test/GetDashboardData**: Dados para dashboard de testes
- **Test/UpdateQuota**: Atualizar cotas de teste
- **Admin/ManageTestLimits**: Gerenciar limites de teste (admin)

#### Jobs/Cron:
- **ResetDailyTestLimits**: Executa diariamente, reseta contadores
- **ResetMonthlyTestLimits**: Executa mensalmente, reseta contadores

## Previsão de PR

### Enums
```
app/Enums/TestResultStatus.php (novo)
app/Enums/TestErrorType.php (novo)
app/Enums/TestLimitPeriod.php (novo)
```

### Models
```
app/Models/CampaignTestLog.php (novo)
app/Models/TestErrorPattern.php (novo)
app/Models/TestRateLimit.php (novo)
app/Models/Organization.php (modificado - campos de limite)
```

### Domains
```
app/Domains/ChatBot/CampaignTester.php (novo)
app/Domains/ChatBot/TemplateTester.php (novo)
app/Domains/ChatBot/TestResult.php (novo)
app/Domains/ChatBot/TestParameterProcessor.php (novo)
app/Domains/ChatBot/TestLogger.php (novo)
app/Domains/ChatBot/TestHistoryAnalyzer.php (novo)
app/Domains/ChatBot/TestErrorClassifier.php (novo)
app/Domains/ChatBot/TestReportGenerator.php (novo)
app/Domains/ChatBot/TestRateLimiter.php (novo)
app/Domains/ChatBot/PhoneValidator.php (novo)
app/Domains/ChatBot/TestDashboard.php (novo)
app/Domains/ChatBot/TestQuotaManager.php (novo)
```

### Factories
```
app/Factories/ChatBot/CampaignTestLogFactory.php (novo)
app/Factories/ChatBot/TestErrorPatternFactory.php (novo)
app/Factories/ChatBot/TestRateLimitFactory.php (novo)
```

### Repositories
```
app/Repositories/CampaignTestLogRepository.php (novo)
app/Repositories/TestErrorPatternRepository.php (novo)
app/Repositories/TestRateLimitRepository.php (novo)
```

### Use Cases
```
app/UseCases/ChatBot/Campaign/TestSend.php (novo)
app/UseCases/ChatBot/Campaign/ValidateForTest.php (novo)
app/UseCases/ChatBot/Template/TestSend.php (novo)
app/UseCases/ChatBot/Test/ProcessParameters.php (novo)
app/UseCases/ChatBot/Test/SendToWhatsApp.php (novo)
app/UseCases/ChatBot/Test/GetTestHistory.php (novo)
app/UseCases/ChatBot/Test/GetTestDetails.php (novo)
app/UseCases/ChatBot/Test/AnalyzeTestResults.php (novo)
app/UseCases/ChatBot/Test/GenerateTestReport.php (novo)
app/UseCases/ChatBot/Test/GetOrganizationSummary.php (novo)
app/UseCases/ChatBot/Test/CheckRateLimit.php (novo)
app/UseCases/ChatBot/Test/ValidatePhoneNumber.php (novo)
app/UseCases/ChatBot/Test/GetDashboardData.php (novo)
app/UseCases/ChatBot/Test/UpdateQuota.php (novo)
app/UseCases/ChatBot/Admin/ManageTestLimits.php (novo)
```

### Controllers
```
app/Http/Controllers/ChatBot/CampaignController.php (modificado - endpoint test-send)
app/Http/Controllers/ChatBot/TemplateController.php (modificado - endpoint test-send)
app/Http/Controllers/ChatBot/CampaignTestController.php (novo)
app/Http/Controllers/ChatBot/TestDashboardController.php (novo)
```

### Requests
```
app/Http/Requests/Campaign/TestSendRequest.php (novo)
app/Http/Requests/Template/TestSendRequest.php (novo)
app/Http/Requests/Test/ValidatePhoneRequest.php (novo)
```

### Jobs
```
app/Jobs/CleanupTestLogs.php (novo)
app/Jobs/AnalyzeTestPatterns.php (novo)
app/Jobs/ResetDailyTestLimits.php (novo)
app/Jobs/ResetMonthlyTestLimits.php (novo)
```

### Migrations
```
database/migrations/xxxx_create_campaign_test_logs_table.php (novo)
database/migrations/xxxx_create_test_error_patterns_table.php (novo)
database/migrations/xxxx_create_test_rate_limits_table.php (novo)
database/migrations/xxxx_add_test_limits_to_organizations_table.php (novo)
database/migrations/xxxx_add_test_indexes.php (novo)
```

**Total Estimado: ~35 arquivos (32 novos + 3 modificados)**

## Melhorias Específicas Identificadas

### Problema 1: Impossibilidade de Testar Campanhas Antes do Envio
**Situação Atual**: Não existe endpoint ou funcionalidade para testar campanhas antes do envio em massa. Usuários precisam criar campanhas reais com clientes para validar templates e parâmetros, contaminando dados de produção.
**Impacto**: Campanhas são enviadas com erros para toda a base de clientes, templates incorretos chegam aos destinatários, debugging é reativo ao invés de preventivo, e qualidade das campanhas fica comprometida.
**Solução**: Endpoint `/test-send` que permite envio para número específico com parâmetros customizados, retornando resultado em tempo real sem afetar base de dados de produção.

### Problema 2: Debugging Reativo de Problemas
**Situação Atual**: Problemas em campanhas só são descobertos após envio em massa, quando usuários reportam erros ou campanhas falham completamente. Não há forma de validar previamente.
**Impacto**: Perda de credibilidade com clientes, campanhas desperdiçadas, tempo excessivo para identificar e corrigir problemas, e impacto negativo na experiência do usuário final.
**Solução**: Sistema de teste preventivo com logs detalhados, classificação automática de erros e sugestões de correção em tempo real.

### Problema 3: Ausência de Histórico de Validações
**Situação Atual**: Não existe registro de testes ou validações realizadas, impossibilitando análise de padrões de erro, melhoria contínua ou troubleshooting baseado em histórico.
**Impacto**: Erros recorrentes não são detectados, não há aprendizado organizacional sobre problemas comuns, e cada problema precisa ser investigado do zero.
**Solução**: Sistema completo de logs de teste com histórico, análise de padrões e relatórios para melhoria contínua da qualidade.

## Plano de Testes

### Testes Unitários:
- Processamento de templates com parâmetros customizados de teste
- Validação de números de telefone para diferentes formatos e países
- Sistema de rate limiting por organização e usuário
- Classificação automática de erros de teste por tipo e severidade
- Geração de logs detalhados com todos os dados necessários
- Cálculo de métricas de sucesso/falha de testes
- Validações de entrada para parâmetros de teste

### Testes de Integração:
- Fluxo completo de teste: recebimento → processamento → envio WhatsApp → retorno
- Integração com WhatsApp API para envio real de testes
- Sistema de logs capturando resposta completa da API
- Rate limiting funcionando corretamente com múltiplos usuários
- Histórico de testes sendo salvo e recuperado corretamente
- Dashboard de testes exibindo dados em tempo real

### Testes de Performance:
- Endpoint de teste respondendo em menos de 5 segundos
- Sistema suportando 100+ testes simultâneos sem degradação
- Queries de histórico otimizadas para grandes volumes
- Limpeza de logs antigos sem impacto na performance
- Rate limiting com baixa latência mesmo com muitas verificações

### Testes de Regressão:
- Sistema de campanhas normal não é afetado pelos testes
- Envio real de campanhas continua funcionando normalmente
- Templates existentes mantêm compatibilidade total
- APIs de campanha existentes não são impactadas
- Dados de produção permanecem isolados dos testes

## Conclusão

Esta Epic revoluciona a qualidade e confiabilidade das campanhas WhatsApp ao permitir validação preventiva antes do envio em massa. A implementação de testes em tempo real com logs detalhados transforma o processo de debugging de reativo para preventivo, melhorando significativamente a experiência do usuário.

### Benefícios Esperados:
- **Qualidade**: Campanhas testadas e validadas antes do envio em massa
- **Prevenção**: Problemas identificados e corrigidos antes de afetar clientes
- **Eficiência**: Debugging rápido com logs detalhados e classificação automática
- **Aprendizado**: Histórico de testes permitindo melhoria contínua
- **Confiança**: Usuários seguros sobre qualidade das campanhas
- **Produtividade**: Redução drástica de tempo gasto corrigindo problemas

### Impacto no Negócio:
- Redução de 90% em campanhas enviadas com erros
- Melhoria de 80% na qualidade percebida das campanhas
- Redução de 70% no tempo de debugging de problemas
- Aumento de 60% na confiança dos usuários no sistema
- Redução de 50% em tickets de suporte relacionados a campanhas falhadas
- Melhoria significativa na satisfação do cliente final

### Métricas de Sucesso:
- Tempo de resposta do endpoint de teste <3 segundos
- 95% dos problemas detectados antes do envio em massa
- 100% dos testes com logs detalhados capturados
- Rate limiting funcionando sem falsos positivos
- Histórico de testes disponível por 30 dias
- Dashboard de testes atualizado em tempo real

## Referências

- WhatsApp Business API Testing Guidelines - https://developers.facebook.com/docs/whatsapp/api/test
- Real-time API Response Patterns - https://restfulapi.net/
- Rate Limiting Best Practices - https://cloud.google.com/architecture/rate-limiting-strategies-techniques
- Template Processing and Validation - Documentação interna de templates
- Epic Campanhas WhatsApp v2.0 (documento base) - storage/internal_confluence/schedule/EPICs/ChatBot/campanhas-whatsapp-v2.md
- Documentação Templates WhatsApp - storage/internal_confluence/services/Meta/templates-whatsapp.md
