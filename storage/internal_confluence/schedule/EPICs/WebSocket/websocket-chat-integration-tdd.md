# Technical Design Document: Integração de WebSocket em Chats de ExchangedMessage

## 📋 **Informações Gerais**

| Campo | Valor |
|-------|-------|
| **Título** | WebSocket Chat Integration - Substituição de Polling por Tempo Real |
| **Tipo** | Technical Design Document (TDD) |
| **Prioridade** | Alta |
| **Complexidade** | Média-Alta |
| **Estimativa** | 3-4 semanas (1 desenvolvedor) |
| **Dependências** | ExchangedMessage system (100% implementado) |
| **Stakeholders** | Equipe de Desenvolvimento, Frontend Team, DevOps |

---

## 🎯 **Objetivo Técnico**

Projetar e especificar a implementação de WebSockets com Laravel Broadcasting para substituir o mecanismo atual de polling do chatByClient, reduzindo carga do servidor em 95%+ e proporcionando experiência de chat em tempo real verdadeiro.

---

## 📊 **Análise do Estado Atual**

### **Sistema Atual de Chat**
- ✅ ExchangedMessage domain completo
- ✅ API REST `/api/exchanged_messages/chat-by-client/{client_id}`
- ✅ GetChatByClient UseCase funcional
- ✅ Webhook processing para inbound/outbound messages
- ✅ Frontend fazendo polling a cada 1s
- ⚠️ Broadcasting configurado mas não utilizado (driver 'null')

### **Problemas Identificados**
- ❌ 60 requisições/minuto por usuário ativo
- ❌ Delay de até 1s para novas mensagens
- ❌ Escalabilidade limitada
- ❌ Impossibilidade de funcionalidades avançadas (typing, online status)

---

## 🏗️ **Arquitetura Técnica Detalhada**

### **1. WebSocket Infrastructure**

#### **Pacote e Configuração**
```bash
composer require beyondcode/laravel-websockets
php artisan vendor:publish --provider="BeyondCode\LaravelWebSockets\WebSocketsServiceProvider" --tag="migrations"
php artisan migrate
```

#### **Broadcasting Configuration**
```php
// config/broadcasting.php
'default' => env('BROADCAST_DRIVER', 'pusher'),

'connections' => [
    'pusher' => [
        'driver' => 'pusher',
        'key' => env('PUSHER_APP_KEY'),
        'secret' => env('PUSHER_APP_SECRET'),
        'app_id' => env('PUSHER_APP_ID'),
        'options' => [
            'cluster' => env('PUSHER_APP_CLUSTER'),
            'host' => '127.0.0.1',
            'port' => 6001,
            'scheme' => 'http',
            'encrypted' => false,
        ],
    ],
],
```

#### **Environment Variables**
```env
BROADCAST_DRIVER=pusher
PUSHER_APP_ID=local
PUSHER_APP_KEY=local
PUSHER_APP_SECRET=local
PUSHER_APP_CLUSTER=mt1
```

### **2. Event System Architecture**

#### **MessageSent Event**
```php
// app/Events/ChatBot/MessageSent.php
class MessageSent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $exchangedMessage;
    public $clientId;
    public $organizationId;

    public function __construct(ExchangedMessage $exchangedMessage)
    {
        $this->exchangedMessage = $exchangedMessage;
        $this->clientId = $exchangedMessage->client_id;
        $this->organizationId = $exchangedMessage->organization_id;
    }

    public function broadcastOn()
    {
        return new PrivateChannel("chat.{$this->clientId}");
    }

    public function broadcastAs()
    {
        return 'message.sent';
    }

    public function broadcastWith()
    {
        return [
            'message' => $this->exchangedMessage->toBroadcastArray(),
            'timestamp' => now()->toISOString(),
        ];
    }
}
```

#### **Channel Authorization**
```php
// routes/channels.php
Broadcast::channel('chat.{clientId}', function ($user, $clientId) {
    // Verificar se o usuário tem acesso ao cliente
    $client = app(ClientRepository::class)->fetchById($clientId);
    return $user->organization_id === $client->organization_id;
});
```

### **3. Domain Integration**

#### **ExchangedMessage Domain Enhancement**
```php
// app/Domains/ChatBot/ExchangedMessage.php
public function toBroadcastArray(): array
{
    return [
        'id' => $this->id,
        'client_id' => $this->client_id,
        'message' => $this->message,
        'inbound' => $this->inbound,
        'outbound' => $this->outbound,
        'sent_at' => $this->sent_at?->toISOString(),
        'created_at' => $this->created_at?->toISOString(),
        'client' => $this->client?->toArray(),
        'phone_number' => $this->phone_number?->toArray(),
    ];
}
```

### **4. UseCase Integration**

#### **Modified SaveFromWebhook**
```php
// app/UseCases/ChatBot/ExchangedMessage/SaveFromWebhook.php
public function perform(/* parameters */)
{
    // ... existing logic ...
    
    $savedExchangedMessage = $this->exchangedMessageRepository->store($exchangedMessage);
    
    // Dispatch WebSocket event
    event(new MessageSent($savedExchangedMessage));
    
    return [
        'success' => true,
        'exchanged_message_id' => $savedExchangedMessage->id,
        'broadcasted' => true
    ];
}
```

#### **Modified SaveOutboundFromStatusUpdate**
```php
// app/UseCases/ChatBot/ExchangedMessage/SaveOutboundFromStatusUpdate.php
public function perform(/* parameters */)
{
    // ... existing logic ...
    
    $savedExchangedMessage = $this->exchangedMessageRepository->store($exchangedMessage);
    
    // Dispatch WebSocket event
    event(new MessageSent($savedExchangedMessage));
    
    return [
        'success' => true,
        'exchanged_message_id' => $savedExchangedMessage->id,
        'broadcasted' => true
    ];
}
```

### **5. Frontend Integration**

#### **Laravel Echo Setup**
```javascript
// resources/js/bootstrap.js
import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

window.Pusher = Pusher;

window.Echo = new Echo({
    broadcaster: 'pusher',
    key: import.meta.env.VITE_PUSHER_APP_KEY,
    cluster: import.meta.env.VITE_PUSHER_APP_CLUSTER,
    wsHost: window.location.hostname,
    wsPort: 6001,
    wssPort: 6001,
    forceTLS: false,
    enabledTransports: ['ws', 'wss'],
});
```

#### **Chat Component Integration**
```javascript
// Chat component
class ChatComponent {
    constructor(clientId) {
        this.clientId = clientId;
        this.setupWebSocket();
        this.setupPollingFallback();
    }

    setupWebSocket() {
        this.channel = window.Echo.private(`chat.${this.clientId}`)
            .listen('message.sent', (e) => {
                this.addMessageToChat(e.message);
                this.stopPolling(); // Stop fallback polling
            })
            .error((error) => {
                console.warn('WebSocket error, falling back to polling:', error);
                this.startPollingFallback();
            });
    }

    setupPollingFallback() {
        this.pollingInterval = null;
        this.pollingEnabled = true;
    }

    startPollingFallback() {
        if (this.pollingEnabled && !this.pollingInterval) {
            this.pollingInterval = setInterval(() => {
                this.fetchNewMessages();
            }, 2000); // Slower fallback polling
        }
    }

    stopPolling() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
    }
}
```

---

## 🔧 **Especificações Técnicas de Implementação**

### **1. Arquitetura de Eventos**

#### **Event Broadcasting Pattern**
- **Padrão**: Observer Pattern com Laravel Events
- **Trigger**: Automático após persistência de ExchangedMessage
- **Channel Strategy**: Private channels por client_id
- **Serialization**: JSON com estrutura otimizada para frontend

#### **Channel Security Model**
- **Authorization**: Baseada em organization_id do usuário autenticado
- **Validation**: Verificação de ownership do cliente
- **Fallback**: Negação de acesso para organizações diferentes

### **2. Data Flow Architecture**

#### **Inbound Message Flow**
```
Webhook → SaveFromWebhook → ExchangedMessage.store() → MessageSent Event → WebSocket Broadcast → Frontend
```

#### **Outbound Message Flow**
```
Status Update → SaveOutboundFromStatusUpdate → ExchangedMessage.store() → MessageSent Event → WebSocket Broadcast → Frontend
```

#### **Manual Message Flow**
```
API Request → Store UseCase → ExchangedMessage.store() → MessageSent Event → WebSocket Broadcast → Frontend
```

### **3. Performance Specifications**

#### **Latency Requirements**
- **Event Dispatch**: < 10ms após persistência
- **WebSocket Delivery**: < 100ms end-to-end
- **Channel Authorization**: < 50ms
- **Fallback Activation**: < 2s após WebSocket failure

#### **Throughput Requirements**
- **Concurrent Users**: 500+ simultâneos
- **Messages/Second**: 1000+ por servidor
- **Memory Usage**: < 512MB para servidor WebSocket
- **CPU Usage**: < 50% em carga normal

### **4. Error Handling Strategy**

#### **WebSocket Failures**
- **Detection**: Connection timeout após 30s
- **Fallback**: Automatic polling activation
- **Recovery**: Reconnection attempts com exponential backoff
- **Logging**: Structured logs para debugging

#### **Broadcasting Failures**
- **Queue Fallback**: Events em queue se broadcast falhar
- **Retry Logic**: 3 tentativas com delay incremental
- **Dead Letter**: Logs detalhados para eventos falhados
- **Monitoring**: Alertas para taxa de falha > 5%

---

## 📊 **Especificações de Monitoramento e Observabilidade**

### **Métricas de Performance**
- **WebSocket Connections**: Número de conexões ativas
- **Message Throughput**: Mensagens/segundo processadas
- **Event Dispatch Time**: Tempo de dispatch de eventos
- **Channel Authorization Time**: Tempo de autorização de canais
- **Memory Usage**: Uso de memória do servidor WebSocket

### **Métricas de Qualidade**
- **Message Delivery Rate**: Taxa de entrega de mensagens
- **Connection Success Rate**: Taxa de sucesso de conexões
- **Fallback Activation Rate**: Frequência de ativação do fallback
- **Error Rate**: Taxa de erros por tipo
- **Reconnection Success Rate**: Taxa de sucesso de reconexões

### **Alertas e Thresholds**
- **High Error Rate**: > 5% de falhas em 5 minutos
- **High Latency**: > 200ms de latência média
- **Memory Usage**: > 80% da memória disponível
- **Connection Drops**: > 10% de drops em 5 minutos
- **Service Unavailable**: Servidor WebSocket down

### **Logging Strategy**
- **Structured Logs**: JSON format com contexto completo
- **Log Levels**: DEBUG para desenvolvimento, INFO para produção
- **Retention**: 30 dias para logs de aplicação
- **Correlation IDs**: Tracking de mensagens end-to-end

---

## 🔒 **Especificações de Segurança**

### **Channel Authorization**
- **Authentication**: Bearer token validation
- **Authorization**: Organization-based access control
- **Rate Limiting**: 100 requests/minute por usuário
- **CORS**: Configuração restritiva para domínios permitidos

### **Data Protection**
- **Encryption**: TLS 1.3 para conexões WebSocket
- **Data Sanitization**: Escape de dados antes do broadcast
- **PII Handling**: Não transmitir dados sensíveis via WebSocket
- **Audit Trail**: Log de todas as autorizações de canal

### **Infrastructure Security**
- **Firewall Rules**: Porta 6001 restrita a load balancer
- **Process Isolation**: Servidor WebSocket em container separado
- **Resource Limits**: CPU e memória limitados por processo
- **Health Checks**: Verificação contínua de saúde do serviço

---

## 🔧 **Infraestrutura e Deploy**

### **Desenvolvimento**
```bash
# Iniciar servidor WebSocket
php artisan websockets:serve

# Monitorar conexões
php artisan websockets:statistics
```

### **Produção**
```bash
# Supervisor configuration
[program:websockets]
command=php /path/to/artisan websockets:serve
directory=/path/to/project
autostart=true
autorestart=true
user=www-data
```

### **Nginx Proxy**
```nginx
location /app/ {
    proxy_pass http://127.0.0.1:6001;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_cache_bypass $http_upgrade;
}
```

---

## 🏗️ **Especificações de Deployment e Infraestrutura**

### **Ambiente de Desenvolvimento**
```bash
# Dependências
composer require beyondcode/laravel-websockets
npm install laravel-echo pusher-js

# Configuração
BROADCAST_DRIVER=pusher
PUSHER_APP_ID=local
PUSHER_APP_KEY=local
PUSHER_APP_SECRET=local
PUSHER_HOST=127.0.0.1
PUSHER_PORT=6001
PUSHER_SCHEME=http

# Execução
php artisan websockets:serve --port=6001
```

### **Ambiente de Produção**

#### **Supervisor Configuration**
```ini
[program:websockets]
command=php /var/www/html/artisan websockets:serve --port=6001
directory=/var/www/html
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/supervisor/websockets.log
```

#### **Nginx Proxy Configuration**
```nginx
location /app/ {
    proxy_pass http://127.0.0.1:6001;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
    proxy_read_timeout 86400;
}
```

#### **Environment Variables Produção**
```env
BROADCAST_DRIVER=pusher
PUSHER_APP_ID=production_app_id
PUSHER_APP_KEY=production_key
PUSHER_APP_SECRET=production_secret
PUSHER_HOST=your-domain.com
PUSHER_PORT=443
PUSHER_SCHEME=https
```

### **Health Checks e Monitoring**
```bash
# Health check endpoint
curl -f http://localhost:6001/app/statistics || exit 1

# Process monitoring
ps aux | grep websockets:serve | grep -v grep || exit 1

# Memory usage check
free -m | awk 'NR==2{printf "%.2f%%\n", $3*100/$2}'
```

---

## 📋 **Acceptance Criteria Técnicos**

### **Funcionalidade Core**
- [ ] WebSocket server inicia sem erros
- [ ] Eventos MessageSent são disparados automaticamente
- [ ] Channels são autorizados corretamente por organização
- [ ] Frontend recebe mensagens em tempo real
- [ ] Fallback para polling funciona automaticamente

### **Performance**
- [ ] Latência < 100ms para entrega de mensagens
- [ ] Suporte para 500+ conexões simultâneas
- [ ] Uso de memória < 512MB por processo
- [ ] CPU usage < 50% em carga normal
- [ ] 95% redução em requisições HTTP

### **Segurança**
- [ ] Autorização baseada em organization_id
- [ ] Conexões TLS em produção
- [ ] Rate limiting implementado
- [ ] Logs de auditoria funcionais

### **Reliability**
- [ ] Reconexão automática após falhas
- [ ] Fallback para polling em < 2s
- [ ] Zero downtime durante deploy
- [ ] Logs estruturados para debugging

---

## 📚 **Documentação e Referências**

- Laravel Broadcasting: https://laravel.com/docs/broadcasting
- Laravel WebSockets: https://beyondco.de/docs/laravel-websockets
- Laravel Echo: https://laravel.com/docs/broadcasting#client-side-installation
- WebSocket RFC 6455: https://tools.ietf.org/html/rfc6455
