# WebSocket Chat Integration - Tickets de Implementação

## 📋 **Informações do Epic**

| Campo | Valor |
|-------|-------|
| **Epic** | Integração de WebSocket em Chats de ExchangedMessage |
| **Objetivo** | Substituir polling por WebSocket para chat em tempo real |
| **Total Tickets** | 6 tickets técnicos |
| **Estimativa Total** | 10 dias (2 semanas) |

### **🎯 Objetivo Principal**
Substituir o polling (requisições GET a cada 1s) por WebSocket para:
- Reduzir carga no servidor em 95%+
- Melhorar experiência do usuário com mensagens instantâneas
- Criar base escalável para futuras funcionalidades

### **📊 Métricas de Sucesso**
- Latência de mensagens < 100ms
- Redução de 95%+ nas requisições HTTP
- Fallback automático para polling funcionando
- Cobertura de testes > 90%

---

## 🎫 **TICKET WS-001: Configurar Infraestrutura WebSocket Base**

### **Descrição**
Configurar infraestrutura completa de WebSocket incluindo Laravel WebSockets, broadcasting, configurações de ambiente e dependências necessárias para suportar comunicação em tempo real.

### **Acceptance Criteria**
- [ ] Laravel WebSockets instalado e configurado
- [ ] Broadcasting configurado com driver pusher
- [ ] Variáveis de ambiente configuradas para dev/prod
- [ ] Comando websockets:serve funcionando
- [ ] Endpoint de estatísticas acessível
- [ ] Configurações de CORS implementadas
- [ ] Logs estruturados configurados

### **Implementação Técnica**

#### **1. Instalação de Dependências**
```bash
# Instalar Laravel WebSockets
composer require beyondcode/laravel-websockets

# Publicar configurações
php artisan vendor:publish --provider="BeyondCode\LaravelWebSockets\WebSocketsServiceProvider" --tag="migrations"
php artisan vendor:publish --provider="BeyondCode\LaravelWebSockets\WebSocketsServiceProvider" --tag="config"

# Executar migrations
php artisan migrate
```

#### **2. Configuração de Broadcasting**
**Arquivo**: `config/broadcasting.php`
```php
'connections' => [
    'pusher' => [
        'driver' => 'pusher',
        'key' => env('PUSHER_APP_KEY'),
        'secret' => env('PUSHER_APP_SECRET'),
        'app_id' => env('PUSHER_APP_ID'),
        'options' => [
            'cluster' => env('PUSHER_APP_CLUSTER'),
            'host' => env('PUSHER_HOST', '127.0.0.1'),
            'port' => env('PUSHER_PORT', 6001),
            'scheme' => env('PUSHER_SCHEME', 'http'),
            'encrypted' => env('PUSHER_SCHEME', 'http') === 'https',
            'useTLS' => env('PUSHER_SCHEME', 'http') === 'https',
        ],
    ],
],
```

#### **3. Variáveis de Ambiente**
**Arquivo**: `.env`
```env
# Broadcasting
BROADCAST_DRIVER=pusher

# Pusher/WebSocket Configuration
PUSHER_APP_ID=local
PUSHER_APP_KEY=local
PUSHER_APP_SECRET=local
PUSHER_APP_CLUSTER=mt1
PUSHER_HOST=127.0.0.1
PUSHER_PORT=6001
PUSHER_SCHEME=http

# Laravel WebSockets
LARAVEL_WEBSOCKETS_PORT=6001
```

#### **4. Comandos de Teste**
```bash
# Iniciar servidor WebSocket
php artisan websockets:serve

# Testar endpoint de estatísticas
curl http://localhost:6001/app/statistics

# Verificar configuração
php artisan config:cache
php artisan config:clear
```

**Estimativa**: 1 dia
**Prioridade**: Alta
**Dependências**: Nenhuma

---

## 🎫 **TICKET WS-002: Implementar Sistema de Eventos e Broadcasting**

### **Descrição**
Criar evento MessageSent broadcastável, configurar autorização de canais por organização e implementar método de serialização otimizada para WebSocket no domain ExchangedMessage.

### **Acceptance Criteria**
- [ ] Evento MessageSent implementado com interface ShouldBroadcast
- [ ] Canal privado configurado como `chat.{client_id}`
- [ ] Autorização de canal baseada em organization_id implementada
- [ ] Método `toBroadcastArray()` adicionado ao ExchangedMessage domain
- [ ] Logs de autorização e eventos implementados
- [ ] Testes unitários para evento e domain criados
- [ ] Error handling para falhas de broadcasting

### **Implementação Técnica**

#### **1. Evento MessageSent**
**Arquivo**: `app/Events/ChatBot/MessageSent.php`
```php
<?php

namespace App\Events\ChatBot;

use App\Domains\ChatBot\ExchangedMessage;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MessageSent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $exchangedMessage;
    public $clientId;
    public $organizationId;

    public function __construct(ExchangedMessage $exchangedMessage)
    {
        $this->exchangedMessage = $exchangedMessage;
        $this->clientId = $exchangedMessage->client_id;
        $this->organizationId = $exchangedMessage->organization_id;
    }

    public function broadcastOn()
    {
        return new PrivateChannel("chat.{$this->clientId}");
    }

    public function broadcastAs()
    {
        return 'message.sent';
    }

    public function broadcastWith()
    {
        return [
            'message' => $this->exchangedMessage->toBroadcastArray(),
            'timestamp' => now()->toISOString(),
            'event_type' => 'new_message',
            'client_id' => $this->clientId,
        ];
    }

    public function broadcastWhen()
    {
        return !is_null($this->exchangedMessage->id);
    }
}
```

#### **2. Autorização de Canal**
**Arquivo**: `routes/channels.php`
```php
<?php

use Illuminate\Support\Facades\Broadcast;
use App\Repositories\ClientRepository;

Broadcast::channel('chat.{clientId}', function ($user, $clientId) {
    try {
        $clientRepository = app(ClientRepository::class);
        $client = $clientRepository->fetchById($clientId);

        $authorized = $user->organization_id === $client->organization_id;

        \Log::info('WebSocket channel authorization', [
            'user_id' => $user->id,
            'organization_id' => $user->organization_id,
            'client_id' => $clientId,
            'client_organization_id' => $client->organization_id,
            'authorized' => $authorized
        ]);

        return $authorized;
    } catch (\Exception $e) {
        \Log::error('WebSocket channel authorization error', [
            'user_id' => $user->id,
            'client_id' => $clientId,
            'error' => $e->getMessage()
        ]);

        return false;
    }
});
```

#### **3. Método toBroadcastArray() no ExchangedMessage**
**Arquivo**: `app/Domains/ChatBot/ExchangedMessage.php`
```php
public function toBroadcastArray(): array
{
    return [
        'id' => $this->id,
        'client_id' => $this->client_id,
        'phone_number_id' => $this->phone_number_id,
        'conversation_id' => $this->conversation_id,
        'message' => $this->message,
        'inbound' => $this->inbound,
        'outbound' => $this->outbound,
        'sent_at' => $this->sent_at?->toISOString(),
        'created_at' => $this->created_at?->toISOString(),
        'client' => $this->client ? [
            'id' => $this->client->id,
            'name' => $this->client->name,
            'phone' => $this->client->phone,
        ] : null,
        'phone_number' => $this->phone_number ? [
            'id' => $this->phone_number->id,
            'whatsapp_phone_number_id' => $this->phone_number->whatsapp_phone_number_id,
        ] : null,
    ];
}
```

**Estimativa**: 1.5 dias
**Prioridade**: Alta
**Dependências**: WS-001

---

## 🎫 **TICKET WS-003: Integrar Backend UseCases com Broadcasting**

### **Descrição**
Modificar os UseCases existentes (SaveFromWebhook, SaveOutboundFromStatusUpdate, Store) para disparar eventos WebSocket automaticamente após salvar ExchangedMessage, incluindo error handling e logs.

### **Acceptance Criteria**
- [ ] SaveFromWebhook dispara MessageSent após salvar
- [ ] SaveOutboundFromStatusUpdate dispara MessageSent após salvar
- [ ] Store dispara MessageSent após salvar
- [ ] Error handling implementado para falhas de broadcasting
- [ ] Logs estruturados para debugging
- [ ] Funcionalidade existente não afetada
- [ ] Response inclui status de broadcasting

### **Implementação Técnica**

#### **1. Modificar SaveFromWebhook UseCase**
**Arquivo**: `app/UseCases/ChatBot/ExchangedMessage/SaveFromWebhook.php`
```php
use App\Events\ChatBot\MessageSent;

public function perform(/* existing parameters */)
{
    try {
        // ... existing logic ...

        $savedExchangedMessage = $this->exchangedMessageRepository->store($exchangedMessage);

        // Dispatch WebSocket event
        try {
            event(new MessageSent($savedExchangedMessage));
            $broadcasted = true;

            \Log::info('WebSocket event dispatched', [
                'exchanged_message_id' => $savedExchangedMessage->id,
                'client_id' => $savedExchangedMessage->client_id,
                'event' => 'MessageSent'
            ]);
        } catch (\Exception $e) {
            $broadcasted = false;

            \Log::error('WebSocket event dispatch failed', [
                'exchanged_message_id' => $savedExchangedMessage->id,
                'client_id' => $savedExchangedMessage->client_id,
                'error' => $e->getMessage()
            ]);
        }

        return [
            'success' => true,
            'reason' => 'Inbound message processed and saved successfully',
            'processed' => 1,
            'exchanged_message_id' => $savedExchangedMessage->id,
            'broadcasted' => $broadcasted
        ];

    } catch (\Exception $e) {
        // ... existing error handling ...
    }
}
```

#### **2. Modificar SaveOutboundFromStatusUpdate UseCase**
**Arquivo**: `app/UseCases/ChatBot/ExchangedMessage/SaveOutboundFromStatusUpdate.php`
```php
use App\Events\ChatBot\MessageSent;

public function perform(/* existing parameters */)
{
    try {
        // ... existing logic ...

        $savedExchangedMessage = $this->exchangedMessageRepository->store($exchangedMessage);

        // Dispatch WebSocket event
        try {
            event(new MessageSent($savedExchangedMessage));
            $broadcasted = true;

            \Log::info('WebSocket event dispatched', [
                'exchanged_message_id' => $savedExchangedMessage->id,
                'client_id' => $savedExchangedMessage->client_id,
                'event' => 'MessageSent',
                'type' => 'outbound'
            ]);
        } catch (\Exception $e) {
            $broadcasted = false;

            \Log::error('WebSocket event dispatch failed', [
                'exchanged_message_id' => $savedExchangedMessage->id,
                'client_id' => $savedExchangedMessage->client_id,
                'error' => $e->getMessage(),
                'type' => 'outbound'
            ]);
        }

        return [
            'success' => true,
            'reason' => 'Outbound message processed and saved successfully',
            'processed' => 1,
            'exchanged_message_id' => $savedExchangedMessage->id,
            'message_id' => $message->id,
            'wam_id' => $statusData['id'] ?? null,
            'broadcasted' => $broadcasted
        ];

    } catch (\Exception $e) {
        // ... existing error handling ...
    }
}
```

#### **3. Modificar Store UseCase**
**Arquivo**: `app/UseCases/ChatBot/ExchangedMessage/Store.php`
```php
use App\Events\ChatBot\MessageSent;

public function perform(StoreRequest $request): ExchangedMessage
{
    $exchangedMessage = $this->exchangedMessageFactory->buildFromStoreRequest($request);
    $savedExchangedMessage = $this->exchangedMessageRepository->store($exchangedMessage);

    // Dispatch WebSocket event
    try {
        event(new MessageSent($savedExchangedMessage));

        \Log::info('WebSocket event dispatched', [
            'exchanged_message_id' => $savedExchangedMessage->id,
            'client_id' => $savedExchangedMessage->client_id,
            'event' => 'MessageSent',
            'type' => 'manual'
        ]);
    } catch (\Exception $e) {
        \Log::error('WebSocket event dispatch failed', [
            'exchanged_message_id' => $savedExchangedMessage->id,
            'client_id' => $savedExchangedMessage->client_id,
            'error' => $e->getMessage(),
            'type' => 'manual'
        ]);
    }

    return $savedExchangedMessage;
}
```

**Estimativa**: 1 dia
**Prioridade**: Alta
**Dependências**: WS-002

---

## 🎫 **TICKET WS-004: Implementar Frontend WebSocket Integration**

### **Descrição**
Configurar Laravel Echo no frontend, implementar componente de chat com WebSocket, fallback automático para polling e indicadores visuais de status de conexão.

### **Acceptance Criteria**
- [ ] Laravel Echo configurado no frontend
- [ ] Conexão WebSocket estabelecida automaticamente
- [ ] Mensagens aparecem instantaneamente no chat
- [ ] Fallback para polling em caso de falha WebSocket
- [ ] Reconexão automática após perda de conexão
- [ ] Indicadores visuais de status de conexão
- [ ] Componente de chat atualizado

### **Implementação Técnica**

#### **1. Instalar Dependências Frontend**
```bash
npm install laravel-echo pusher-js
```

#### **2. Configurar Laravel Echo**
**Arquivo**: `resources/js/bootstrap.js`
```javascript
import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

window.Pusher = Pusher;

window.Echo = new Echo({
    broadcaster: 'pusher',
    key: import.meta.env.VITE_PUSHER_APP_KEY,
    cluster: import.meta.env.VITE_PUSHER_APP_CLUSTER ?? 'mt1',
    wsHost: import.meta.env.VITE_PUSHER_HOST ?? window.location.hostname,
    wsPort: import.meta.env.VITE_PUSHER_PORT ?? 6001,
    wssPort: import.meta.env.VITE_PUSHER_PORT ?? 6001,
    forceTLS: (import.meta.env.VITE_PUSHER_SCHEME ?? 'https') === 'https',
    enabledTransports: ['ws', 'wss'],

    // Configurações de reconexão
    enableStats: false,
    enableLogging: import.meta.env.DEV,

    // Auth endpoint
    authEndpoint: '/broadcasting/auth',
    auth: {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('auth_token')}`,
        },
    },
});

// Event listeners globais para debugging
if (import.meta.env.DEV) {
    window.Echo.connector.pusher.connection.bind('connected', () => {
        console.log('WebSocket connected');
    });

    window.Echo.connector.pusher.connection.bind('disconnected', () => {
        console.log('WebSocket disconnected');
    });

    window.Echo.connector.pusher.connection.bind('error', (error) => {
        console.error('WebSocket error:', error);
    });
}
```

#### **3. Implementar Chat Component com WebSocket**
**Arquivo**: `resources/js/components/ChatComponent.js`
```javascript
class ChatComponent {
    constructor(clientId, authToken) {
        this.clientId = clientId;
        this.authToken = authToken;
        this.isWebSocketConnected = false;
        this.pollingInterval = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;

        this.setupWebSocket();
        this.setupPollingFallback();
    }

    setupWebSocket() {
        try {
            this.channel = window.Echo.private(`chat.${this.clientId}`)
                .listen('message.sent', (event) => {
                    console.log('New message received via WebSocket:', event);
                    this.addMessageToChat(event.message);
                    this.stopPolling();
                    this.isWebSocketConnected = true;
                    this.updateConnectionStatus('connected');
                    this.reconnectAttempts = 0;
                })
                .error((error) => {
                    console.warn('WebSocket error, falling back to polling:', error);
                    this.isWebSocketConnected = false;
                    this.updateConnectionStatus('error');
                    this.startPollingFallback();
                });

            // Connection events
            this.channel.subscribed(() => {
                console.log('Successfully subscribed to chat channel');
                this.isWebSocketConnected = true;
                this.updateConnectionStatus('connected');
                this.stopPolling();
            });

        } catch (error) {
            console.error('Failed to setup WebSocket:', error);
            this.startPollingFallback();
        }
    }

    setupPollingFallback() {
        this.pollingEnabled = true;
        this.pollingInterval = null;
    }

    startPollingFallback() {
        if (this.pollingEnabled && !this.pollingInterval) {
            console.log('Starting polling fallback');
            this.updateConnectionStatus('polling');

            this.pollingInterval = setInterval(() => {
                this.fetchNewMessages();
            }, 3000); // Slower fallback polling (3s instead of 1s)
        }
    }

    stopPolling() {
        if (this.pollingInterval) {
            console.log('Stopping polling fallback');
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
    }

    addMessageToChat(message) {
        // Implementar lógica para adicionar mensagem ao DOM
        const chatContainer = document.getElementById('chat-messages');
        const messageElement = this.createMessageElement(message);
        chatContainer.appendChild(messageElement);
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }

    createMessageElement(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${message.inbound ? 'inbound' : 'outbound'}`;
        messageDiv.innerHTML = `
            <div class="message-content">${message.message}</div>
            <div class="message-time">${new Date(message.sent_at).toLocaleTimeString()}</div>
        `;
        return messageDiv;
    }

    updateConnectionStatus(status) {
        const statusElement = document.getElementById('connection-status');
        if (statusElement) {
            statusElement.className = `connection-status ${status}`;
            statusElement.textContent = this.getStatusText(status);
        }
    }

    getStatusText(status) {
        switch (status) {
            case 'connected': return 'Conectado (Tempo Real)';
            case 'polling': return 'Conectado (Polling)';
            case 'error': return 'Reconectando...';
            default: return 'Conectando...';
        }
    }

    fetchNewMessages() {
        // Implementar chamada para API REST como fallback
        fetch(`/api/exchanged_messages/chat-by-client/${this.clientId}?limit=10`, {
            headers: {
                'Authorization': `Bearer ${this.authToken}`,
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Processar novas mensagens
                this.processPollingMessages(data.data);
            }
        })
        .catch(error => {
            console.error('Polling fallback error:', error);
        });
    }

    processPollingMessages(messages) {
        // Implementar lógica para processar mensagens do polling
        // e evitar duplicatas
        messages.forEach(message => {
            if (!this.messageExists(message.id)) {
                this.addMessageToChat(message);
            }
        });
    }

    messageExists(messageId) {
        return document.querySelector(`[data-message-id="${messageId}"]`) !== null;
    }

    disconnect() {
        if (this.channel) {
            window.Echo.leave(`chat.${this.clientId}`);
        }
        this.stopPolling();
    }
}

// Exportar para uso global
window.ChatComponent = ChatComponent;
```

**Estimativa**: 2 dias
**Prioridade**: Alta
**Dependências**: WS-003

---

## 🎫 **TICKET WS-005: Implementar Testes Completos para WebSocket**

### **Descrição**
Criar suite completa de testes unitários, integração e autorização para garantir que a funcionalidade WebSocket funciona corretamente, incluindo testes de performance e cobertura > 90% para código novo.

### **Acceptance Criteria**
- [ ] Testes unitários para evento MessageSent e método toBroadcastArray()
- [ ] Testes de integração para todos os UseCases modificados
- [ ] Testes de autorização de canais WebSocket
- [ ] Testes de performance para múltiplas conexões simultâneas
- [ ] Testes de error handling para falhas de broadcasting
- [ ] Cobertura de testes > 90% para código novo
- [ ] Todos os testes passando sem regressões
- [ ] Testes de fallback para polling

**Estimativa**: 2 dias
**Prioridade**: Alta
**Dependências**: WS-002, WS-003

---

## 🎫 **TICKET WS-006: Configurar Infraestrutura de Produção e Monitoramento**

### **Descrição**
Configurar infraestrutura completa de produção para WebSocket incluindo Supervisor, proxy Nginx/Apache, monitoramento, alertas e documentação de troubleshooting para garantir alta disponibilidade e observabilidade.

### **Acceptance Criteria**
- [ ] Supervisor configurado para manter websockets:serve ativo
- [ ] Proxy Nginx/Apache configurado para WebSocket com SSL
- [ ] Firewall configurado adequadamente
- [ ] Scripts de deploy e health check implementados
- [ ] Monitoramento e alertas configurados
- [ ] Documentação de troubleshooting completa
- [ ] Logs centralizados e estruturados
- [ ] Backup e recovery procedures documentados

**Estimativa**: 2 dias
**Prioridade**: Média
**Dependências**: WS-001, WS-002, WS-003, WS-004

---

## 📊 **Resumo de Entrega**

### **Total de Tickets**: 6
### **Estimativa Total**: 10 dias (2 semanas)

### **Entregáveis por Ticket**:
1. **WS-001**: Infraestrutura WebSocket configurada (1 dia)
2. **WS-002**: Sistema de eventos implementado (1.5 dias)
3. **WS-003**: Backend integrado com broadcasting (1 dia)
4. **WS-004**: Frontend com WebSocket funcional (2 dias)
5. **WS-005**: Testes completos implementados (2 dias)
6. **WS-006**: Infraestrutura de produção configurada (2 dias)

### **Critérios de Aceitação do Epic**:
- [ ] Polling substituído por WebSocket em 100% dos casos
- [ ] Latência de mensagens < 100ms
- [ ] Redução de 95%+ nas requisições HTTP
- [ ] Fallback automático para polling funcionando
- [ ] Cobertura de testes > 90%
- [ ] Infraestrutura de produção estável
- [ ] Documentação completa entregue
- [ ] Monitoramento e alertas ativos

### **Definition of Done**:
- [ ] Todos os 6 tickets completados
- [ ] Todos os testes passando
- [ ] Code review aprovado
- [ ] Deploy em produção realizado
- [ ] Monitoramento ativo e alertas configurados
- [ ] Documentação atualizada e validada
- [ ] Performance targets atingidos
- [ ] Fallback testado e funcionando
