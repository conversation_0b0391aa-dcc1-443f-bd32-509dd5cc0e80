# Epic: Integração de WebSocket em Chats de ExchangedMessage - Substituição de Polling por Tempo Real

## Overview

Esta Epic visa substituir o mecanismo atual de polling (requisições GET periódicas a cada 1s) utilizado na exibição do chatByClient, implementando WebSockets com suporte a Laravel Broadcasting para garantir mensagens em tempo real, reduzir carga no servidor e criar uma base escalável para novas funcionalidades de chat.

### Funcionalidades Atuais Identificadas:

- ✅ **ExchangedMessage Domain**: Estrutura completa para mensagens trocadas
- ✅ **ExchangedMessageController**: API REST com endpoint `chatByClient/{client_id}`
- ✅ **GetChatByClient UseCase**: Busca histórico de mensagens por cliente
- ✅ **ExchangedMessageRepository**: Métodos para buscar mensagens por cliente
- ✅ **SaveFromWebhook UseCase**: Salva mensagens inbound do webhook
- ✅ **SaveOutboundFromStatusUpdate UseCase**: Salva mensagens outbound
- ⚠️ **Frontend Polling**: Consulta API constantemente (1s) para novas mensagens
- ⚠️ **Broadcasting Config**: Configurado mas não utilizado (driver 'null')
- ❌ **WebSocket Events**: Eventos broadcastáveis para mensagens
- ❌ **Real-time Channels**: Canais específicos para chat por cliente
- ❌ **Frontend WebSocket**: Integração com Laravel Echo + PusherJS

### Melhorias Propostas:

#### 1. **Implementação de Broadcasting em Tempo Real**
Configurar Laravel WebSockets e criar eventos broadcastáveis que são disparados automaticamente quando novas ExchangedMessage são persistidas, eliminando a necessidade de polling.

#### 2. **Canais de Chat Específicos**
Criar canais de broadcast organizados por cliente (`chat.{client_id}`) para garantir que apenas mensagens relevantes sejam enviadas para cada usuário.

#### 3. **Integração Frontend com WebSockets**
Substituir o mecanismo de polling no frontend por conexão WebSocket usando Laravel Echo, permitindo recebimento instantâneo de novas mensagens.

#### 4. **Infraestrutura WebSocket Self-Hosted**
Configurar servidor WebSocket usando beyondcode/laravel-websockets para manter controle total sobre a infraestrutura sem dependências externas.

## Resumo do Plano de Implementação

A Epic será implementada em 4 fases sequenciais, cada uma entregando valor incremental e permitindo validação antes da próxima fase.

**Fase 1**: Configuração Base WebSocket - Instalação e configuração do Laravel WebSockets
**Fase 2**: Eventos e Broadcasting - Criação de eventos broadcastáveis para ExchangedMessage
**Fase 3**: Integração Backend - Modificação dos UseCases para disparar eventos
**Fase 4**: Frontend e Infraestrutura - Integração com Laravel Echo e configuração de produção

## Plano de Implementação Detalhado

### 1. Configuração Base WebSocket

#### Dependências e Configuração:
- Instalar `beyondcode/laravel-websockets` via Composer
- Configurar `config/broadcasting.php` para usar driver 'pusher' apontando para Laravel WebSockets
- Atualizar `.env` com configurações WebSocket
- Configurar `routes/channels.php` com canais de autorização

#### Database:
- **Tabela `websockets_statistics_entries`**: Criada automaticamente pelo pacote para estatísticas
- Nenhuma modificação necessária nas tabelas existentes

#### Configuração:
- **Broadcasting Driver**: Alterar de 'null' para 'pusher' no .env
- **WebSocket Server**: Configurar porta 6001 para desenvolvimento
- **Pusher Credentials**: Configurar chaves locais para desenvolvimento

### 2. Eventos e Broadcasting

#### Eventos:
- **MessageSent**: Evento broadcastável disparado quando nova ExchangedMessage é criada
- **MessageUpdated**: Evento para atualizações de status de mensagem (opcional)

#### Canais:
- **chat.{client_id}**: Canal privado para mensagens de cliente específico
- **organization.{organization_id}**: Canal para eventos organizacionais (futuro)

#### Domínios:
- **ExchangedMessage**: Adicionar método `toBroadcastArray()` para serialização
- **ChatEvent**: Novo domínio para encapsular dados de eventos de chat

### 3. Integração Backend

#### UseCases Modificados:
- **SaveFromWebhook**: Disparar evento MessageSent após salvar mensagem inbound
- **SaveOutboundFromStatusUpdate**: Disparar evento MessageSent após salvar mensagem outbound
- **Store**: Disparar evento MessageSent após criação manual

#### Listeners:
- **BroadcastMessageSent**: Listener para processar evento MessageSent e fazer broadcast

#### Jobs/Cron:
- Nenhum job adicional necessário (broadcasting é síncrono por padrão)

### 4. Frontend e Infraestrutura

#### Frontend:
- Instalar Laravel Echo e PusherJS via npm
- Configurar `resources/js/bootstrap.js` com configurações WebSocket
- Modificar componente de chat para escutar eventos ao invés de polling
- Implementar fallback para polling em caso de falha WebSocket

#### Infraestrutura:
- Configurar Supervisor para manter `php artisan websockets:serve` ativo
- Configurar proxy Apache/Nginx para WebSocket (porta 443/80)
- Configurar firewall para porta 6001 se necessário
- Documentar processo de deploy e monitoramento

## Previsão de PR

### Events
```
app/Events/ChatBot/MessageSent.php (novo)
```

### Listeners
```
app/Listeners/ChatBot/BroadcastMessageSent.php (novo)
```

### Domains
```
app/Domains/ChatBot/ExchangedMessage.php (modificado - adicionar toBroadcastArray)
app/Domains/ChatBot/ChatEvent.php (novo)
```

### Use Cases
```
app/UseCases/ChatBot/ExchangedMessage/SaveFromWebhook.php (modificado)
app/UseCases/ChatBot/ExchangedMessage/SaveOutboundFromStatusUpdate.php (modificado)
app/UseCases/ChatBot/ExchangedMessage/Store.php (modificado)
```

### Configuration
```
config/broadcasting.php (modificado)
routes/channels.php (modificado)
```

### Frontend
```
resources/js/bootstrap.js (modificado)
package.json (modificado - adicionar laravel-echo e pusher-js)
```

### Environment
```
.env.example (modificado - adicionar variáveis WebSocket)
```

### Documentation
```
storage/docs/websocket-setup-guide.md (novo)
storage/docs/websocket-troubleshooting.md (novo)
```

**Total Estimado: ~12 arquivos (8 novos + 4 modificados)**

## Melhorias Específicas Identificadas

### Problema 1: Alto Volume de Requisições Desnecessárias
**Situação Atual**: Frontend faz requisições GET para `/api/exchanged_messages/chat-by-client/{client_id}` a cada 1 segundo, mesmo quando não há novas mensagens
**Impacto**: Sobrecarga desnecessária no servidor, consumo de banda, degradação de performance com múltiplos usuários simultâneos
**Solução**: WebSockets mantêm conexão persistente e enviam dados apenas quando há novas mensagens

### Problema 2: Experiência de Usuário Não Fluída
**Situação Atual**: Mensagens aparecem com delay de até 1 segundo, criando experiência de chat não natural
**Impacto**: Usuários percebem lentidão na comunicação, prejudicando a experiência de atendimento
**Solução**: Mensagens aparecem instantaneamente via WebSocket events

### Problema 3: Escalabilidade Limitada
**Situação Atual**: Cada usuário ativo gera 60 requisições por minuto, limitando número de usuários simultâneos
**Impacto**: Servidor pode ficar sobrecarregado com crescimento da base de usuários
**Solução**: WebSockets reduzem drasticamente carga do servidor, permitindo mais usuários simultâneos

### Problema 4: Falta de Base para Funcionalidades Avançadas
**Situação Atual**: Polling não permite implementar funcionalidades como "usuário digitando", status online/offline
**Impacto**: Limitação para evoluir funcionalidades de chat e melhorar experiência
**Solução**: WebSockets criam base para implementar funcionalidades avançadas de chat em tempo real

## Plano de Testes

### Testes Unitários:
- Evento MessageSent com dados corretos
- Método toBroadcastArray() do ExchangedMessage
- Autorização de canais privados
- Fallback para polling quando WebSocket falha

### Testes de Integração:
- Fluxo completo: webhook → save → broadcast → frontend
- Autorização de usuário para canal específico
- Múltiplos clientes recebendo mensagens corretas
- Reconexão automática após perda de conexão

### Testes de Performance:
- 100+ usuários simultâneos conectados via WebSocket
- Latência de entrega de mensagens (< 100ms)
- Uso de memória do servidor WebSocket
- Throughput de mensagens por segundo

### Testes de Regressão:
- API REST chatByClient continua funcionando
- Webhook processing não afetado
- Funcionalidades existentes de ExchangedMessage mantidas
- Compatibilidade com dados existentes

## Conclusão

Esta Epic transformará fundamentalmente a experiência de chat do sistema, eliminando o polling ineficiente e implementando comunicação em tempo real verdadeiro. A mudança reduzirá significativamente a carga do servidor enquanto melhora drasticamente a experiência do usuário.

### Benefícios Esperados:
- **Performance**: Redução de 95%+ nas requisições HTTP para chat
- **Experiência**: Mensagens instantâneas sem delay perceptível
- **Escalabilidade**: Suporte para 10x mais usuários simultâneos
- **Infraestrutura**: Base sólida para funcionalidades avançadas de chat

### Impacto no Negócio:
- Melhoria significativa na experiência de atendimento ao cliente
- Redução de custos de infraestrutura por usuário
- Capacidade de implementar funcionalidades competitivas de chat
- Preparação para crescimento da base de usuários

### Métricas de Sucesso:
- Redução de 95% nas requisições HTTP para chat
- Latência de mensagens < 100ms
- Zero downtime durante implementação
- Suporte para 500+ usuários simultâneos

## Referências

- Laravel Broadcasting Documentation - https://laravel.com/docs/broadcasting
- Laravel WebSockets Package - https://beyondco.de/docs/laravel-websockets
- Laravel Echo Documentation - https://laravel.com/docs/broadcasting#client-side-installation
- PusherJS Documentation - https://pusher.com/docs/channels/library_auth_reference/pusher-js
- WebSocket RFC 6455 - https://tools.ietf.org/html/rfc6455
