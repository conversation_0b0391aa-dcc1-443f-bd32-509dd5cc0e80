# PRD - Sistema de Campanhas WhatsApp v2.0

## 1. Visão Geral

### 1.1 Contexto Atual
O sistema de campanhas WhatsApp v1 está funcional mas apresenta inconsistências críticas que afetam a confiabilidade e experiência do usuário:

- **Status inconsistentes**: Campanhas com 100% de mensagens enviadas/entregues mantêm status incorretos
- **Agendamento não funcional**: Sistema de agendamento precisa ser validado e corrigido
- **Falta de endpoint de teste**: Não há forma de testar campanhas em tempo real
- **Log de erros insuficiente**: Informações de falha não são adequadamente capturadas e exibidas

### 1.2 Objetivos da v2
1. **Confiabilidade**: Status sempre consistentes e atualizados automaticamente
2. **Funcionalidade completa**: Agendamento funcionando perfeitamente
3. **Testabilidade**: Endpoint de teste em tempo real para validação
4. **Observabilidade**: Logs detalhados de erros e falhas para debugging
5. **Manutenibilidade**: Crons para limpeza e monitoramento automático

## 2. Análise do Sistema Atual

### 2.1 Arquitetura Existente
- **Models**: Campaign, Message com enums CampaignStatus e MessageStatus
- **Domains**: Campaign e Message com métodos de status e retry
- **Use Cases**: LaunchCampaign, GenerateMessages, Send, GetMessagesAvailableToSent
- **Jobs**: SendWhatsAppMessages (cron a cada minuto), ProcessMessageRetries, ProactiveWhatsAppSync
- **Controllers**: CampaignController, MessageController

### 2.2 Problemas Identificados

#### 2.2.1 Status Inconsistentes
**Problema**: Campanhas com dados como:
```json
{
  "is_scheduled": false,
  "is_sent": false,
  "is_sending": true,
  "message_count": 0,
  "status": 1,
  "sent_at": "2025-09-11T23:46:34.000000Z"
}
```

**Causa Raiz**: 
- Falta de sincronização entre campos boolean legacy e enum status
- Ausência de job para atualizar status baseado no estado real das mensagens
- `message_count` não é atualizado corretamente

#### 2.2.2 Agendamento
**Status Atual**: 
- Campo `scheduled_at` existe no model Message
- `GetMessagesAvailableToSent` verifica `scheduled_at <= now()`
- Falta validação se o sistema está funcionando corretamente

#### 2.2.3 Logs de Erro
**Limitações Atuais**:
- `last_error_message` na Message
- DBLog genérico no SendWhatsAppMessages
- Falta de estrutura para capturar detalhes específicos do WhatsApp API

## 3. Requisitos Funcionais

### 3.1 RF01 - Sistema de Status Consistente

#### 3.1.1 Cron de Sincronização de Status
**Frequência**: A cada 5 minutos
**Função**: Atualizar status de campanhas baseado no estado real das mensagens

**Lógica**:
```
Para cada campanha não finalizada:
1. Contar mensagens por status (sent, delivered, failed, pending)
2. Calcular percentuais de conclusão
3. Determinar novo status da campanha:
   - COMPLETED: 100% das mensagens sent/delivered
   - FAILED: >50% das mensagens failed e sem pending
   - SENDING: Ainda há mensagens pending
4. Atualizar campos boolean para compatibilidade
5. Atualizar message_count
```

#### 3.1.2 Cron de Monitoramento de Campanhas Antigas
**Frequência**: Diário
**Função**: Identificar campanhas "presas" em status intermediário há muito tempo

**Critérios de Alerta**:
- Campanhas em SENDING há mais de 7 dias
- Campanhas com `is_sending=true` mas sem mensagens pending há mais de 24h
- Campanhas com `sent_at` preenchido mas `is_sent=false`

### 3.2 RF02 - Sistema de Agendamento Robusto

#### 3.2.1 Validação e Correção do Agendamento
**Requisitos**:
1. Validar se `GetMessagesAvailableToSent` respeita `scheduled_at` corretamente
2. Implementar endpoint para agendar campanhas
3. Criar job para processar campanhas agendadas

#### 3.2.2 Endpoint de Agendamento
```http
POST /api/campaigns/{id}/schedule
{
  "scheduled_at": "2025-09-27T10:00:00Z",
  "timezone": "America/Sao_Paulo"
}
```

**Validações**:
- Data futura obrigatória
- Campanha deve estar em status DRAFT
- Timezone válido

### 3.3 RF03 - Endpoint de Teste em Tempo Real

#### 3.3.1 Endpoint de Envio de Teste
```http
POST /api/campaigns/{id}/test-send
{
  "phone_number": "+5511999999999",
  "test_parameters": {
    "client.name": "João Teste",
    "client.email": "<EMAIL>"
  }
}
```

**Resposta**:
```json
{
  "success": true,
  "message_id": "test_msg_123",
  "whatsapp_response": {
    "id": "wamid_xxx",
    "status": "sent"
  },
  "processed_message": "Olá João Teste, seu pedido foi confirmado!",
  "sent_at": "2025-09-26T15:30:00Z",
  "error": null
}
```

**Funcionalidades**:
1. Validar template e parâmetros
2. Processar variáveis em tempo real
3. Enviar via WhatsApp API
4. Retornar resultado imediato
5. Não salvar na base de dados (apenas log)

### 3.4 RF04 - Sistema de Logs Avançado

#### 3.4.1 Estrutura de Logs de Erro
**Novas colunas na tabela messages**:
- `whatsapp_error_code`: Código específico do erro WhatsApp
- `whatsapp_error_details`: JSON com detalhes completos do erro
- `retry_strategy`: Estratégia de retry aplicada
- `final_failure_reason`: Motivo da falha definitiva

#### 3.4.2 Logs em Tempo Real para Teste
**Requisitos**:
- Capturar todos os detalhes da API WhatsApp
- Incluir headers, payload enviado, resposta completa
- Classificar tipos de erro (rate limit, template, número inválido, etc.)
- Sugerir correções quando possível

## 4. Requisitos Técnicos

### 4.1 RT01 - Jobs e Crons

#### 4.1.1 Novos Jobs
```php
// app/Jobs/SyncCampaignStatusFromMessages.php
// Frequência: A cada 5 minutos
// Função: Sincronizar status de campanhas

// app/Jobs/MonitorStuckCampaigns.php  
// Frequência: Diário
// Função: Detectar campanhas presas

// app/Jobs/ProcessScheduledCampaigns.php
// Frequência: A cada minuto
// Função: Processar campanhas agendadas
```

#### 4.1.2 Atualização do Kernel
```php
// app/Console/Kernel.php
$schedule->job(new SyncCampaignStatusFromMessages)->everyFiveMinutes();
$schedule->job(new MonitorStuckCampaigns)->daily();
$schedule->job(new ProcessScheduledCampaigns)->everyMinute();
```

### 4.2 RT02 - Novos Use Cases

```php
// app/UseCases/ChatBot/Campaign/TestSend.php
// app/UseCases/ChatBot/Campaign/Schedule.php
// app/UseCases/ChatBot/Campaign/SyncStatusFromMessages.php
// app/UseCases/ChatBot/Message/SendTest.php
```

### 4.3 RT03 - Melhorias no Repository

#### 4.3.1 CampaignRepository
```php
// Novos métodos
public function fetchCampaignsNeedingStatusSync(): array
public function fetchStuckCampaigns(): array
public function updateStatusFromMessages(int $campaignId): bool
```

#### 4.3.2 MessageRepository  
```php
// Novos métodos
public function getMessageStatsByCampaign(int $campaignId): array
public function fetchScheduledCampaigns(): array
```

## 5. Especificações de Implementação

### 5.1 Fase 1 - Correção de Status (Sprint 1)
1. Implementar `SyncCampaignStatusFromMessages` job
2. Corrigir lógica de `message_count`
3. Adicionar validações de consistência
4. Implementar `MonitorStuckCampaigns`

### 5.2 Fase 2 - Sistema de Agendamento (Sprint 2)  
1. Validar e corrigir `GetMessagesAvailableToSent`
2. Implementar endpoint de agendamento
3. Criar `ProcessScheduledCampaigns` job
4. Testes de agendamento

### 5.3 Fase 3 - Endpoint de Teste (Sprint 3)
1. Implementar `TestSend` use case
2. Criar endpoint `/test-send`
3. Sistema de logs em tempo real
4. Interface para visualizar resultados

### 5.4 Fase 4 - Logs Avançados (Sprint 4)
1. Migração para novas colunas de erro
2. Melhorar captura de erros WhatsApp
3. Dashboard de monitoramento
4. Alertas automáticos

## 6. Critérios de Aceitação

### 6.1 Status Consistente
- [ ] Campanhas com 100% mensagens enviadas têm status COMPLETED
- [ ] `message_count` sempre correto
- [ ] Campos boolean sincronizados com enum
- [ ] Alertas para campanhas presas funcionando

### 6.2 Agendamento
- [ ] Campanhas agendadas não são enviadas antes do horário
- [ ] Campanhas agendadas são processadas no horário correto
- [ ] Timezone respeitado corretamente
- [ ] Interface para agendar campanhas

### 6.3 Teste em Tempo Real
- [ ] Endpoint retorna resultado em <5 segundos
- [ ] Erros detalhados são capturados e exibidos
- [ ] Variáveis são processadas corretamente
- [ ] Não interfere com campanhas reais

### 6.4 Logs e Monitoramento
- [ ] Todos os erros WhatsApp são capturados
- [ ] Logs incluem sugestões de correção
- [ ] Dashboard mostra status em tempo real
- [ ] Alertas automáticos para problemas críticos

## 7. Riscos e Mitigações

### 7.1 Riscos Técnicos
- **Sobrecarga de jobs**: Mitigar com rate limiting e processamento em chunks
- **Inconsistência durante migração**: Implementar rollback automático
- **Performance**: Otimizar queries e usar índices apropriados

### 7.2 Riscos de Negócio  
- **Interrupção do serviço**: Deploy gradual com feature flags
- **Perda de mensagens**: Backup completo antes de mudanças críticas
- **Custos de API**: Monitoramento de uso e limites

## 8. Métricas de Sucesso

### 8.1 Métricas Técnicas
- Redução de 95% em campanhas com status inconsistente
- 100% de campanhas agendadas executadas no horário correto
- Tempo de resposta do endpoint de teste <3 segundos
- 0 campanhas presas por mais de 24h

### 8.2 Métricas de Negócio
- Redução de 80% em tickets de suporte relacionados a status
- Aumento de 50% na confiança do sistema de agendamento
- Redução de 60% no tempo de debugging de campanhas falhadas

## 9. Detalhamento Técnico dos Problemas Atuais

### 9.1 Análise do Problema de Status

#### 9.1.1 Cenário Problemático Identificado
```json
{
  "is_scheduled": false,
  "is_sent": false,
  "is_sending": true,
  "message_count": 0,
  "status": 1,
  "sent_at": "2025-09-11T23:46:34.000000Z"
}
```

**Inconsistências Detectadas**:
1. `is_sending=true` mas `sent_at` preenchido (contraditório)
2. `message_count=0` mas campanha foi "enviada"
3. `status=1` (DRAFT) mas `sent_at` preenchido
4. `is_sent=false` mas `sent_at` preenchido

#### 9.1.2 Causa Raiz Identificada
Analisando o código atual:

<augment_code_snippet path="app/UseCases/ChatBot/Campaign/LaunchCampaign.php" mode="EXCERPT">
````php
$campaign->is_sending = true;
$campaign->sent_at = Carbon::now();
$this->campaignRepository->update($campaign, $organization_id);
````
</augment_code_snippet>

**Problema**: O `LaunchCampaign` define `sent_at` no momento do lançamento, não quando realmente enviado.

### 9.2 Análise do Sistema de Agendamento

#### 9.2.1 Implementação Atual
<augment_code_snippet path="app/Repositories/MessageRepository.php" mode="EXCERPT">
````php
->where( function ($query) {
    $query->whereNull("scheduled_at")->orWhere("scheduled_at", "<=", now());
})
````
</augment_code_snippet>

**Status**: ✅ Lógica básica está correta
**Necessário**: Validar se campanhas agendadas estão sendo criadas corretamente

#### 9.2.2 Gaps Identificados
1. Não há endpoint para agendar campanhas
2. Não há validação de timezone
3. Não há job específico para processar campanhas agendadas
4. Falta interface para gerenciar agendamentos

### 9.3 Análise de Logs de Erro

#### 9.3.1 Estrutura Atual
<augment_code_snippet path="app/Models/Message.php" mode="EXCERPT">
````php
'last_error_message',
'delivery_attempts',
'max_retries',
'next_retry_at',
````
</augment_code_snippet>

**Limitações**:
- `last_error_message` é texto simples
- Não captura códigos específicos do WhatsApp
- Não diferencia tipos de erro
- Não oferece sugestões de correção

## 10. Soluções Propostas Detalhadas

### 10.1 Solução para Status Inconsistente

#### 10.1.1 Job SyncCampaignStatusFromMessages
```php
class SyncCampaignStatusFromMessages implements ShouldQueue
{
    public function handle()
    {
        // Buscar campanhas não finalizadas
        $campaigns = Campaign::whereIn('status', [
            CampaignStatus::SENDING,
            CampaignStatus::SCHEDULED
        ])->get();

        foreach ($campaigns as $campaign) {
            $this->syncCampaignStatus($campaign);
        }
    }

    private function syncCampaignStatus(Campaign $campaign)
    {
        $stats = $this->getMessageStats($campaign->id);

        $newStatus = $this->determineStatus($stats);

        if ($campaign->status !== $newStatus) {
            $campaign->updateStatus($newStatus, 'Auto-sync from messages');
        }

        // Atualizar message_count
        $campaign->message_count = $stats['total'];
        $campaign->save();
    }
}
```

#### 10.1.2 Lógica de Determinação de Status
```php
private function determineStatus(array $stats): CampaignStatus
{
    $total = $stats['total'];
    $sent = $stats['sent'] + $stats['delivered'] + $stats['read'];
    $failed = $stats['failed'];
    $pending = $stats['pending'];

    if ($total === 0) {
        return CampaignStatus::DRAFT;
    }

    if ($pending === 0) {
        if ($failed > $sent) {
            return CampaignStatus::FAILED;
        }
        return CampaignStatus::COMPLETED;
    }

    return CampaignStatus::SENDING;
}
```

### 10.2 Solução para Agendamento

#### 10.2.1 Endpoint de Agendamento
```php
class CampaignController extends Controller
{
    public function schedule(int $id, ScheduleRequest $request): JsonResponse
    {
        $useCase = app()->make(ScheduleCampaign::class);
        $campaign = $useCase->perform($id, $request->validated());

        return $this->response(
            "Campaign scheduled successfully",
            "success",
            200,
            $campaign->toArray()
        );
    }
}
```

#### 10.2.2 Use Case ScheduleCampaign
```php
class ScheduleCampaign
{
    public function perform(int $campaignId, array $data): Campaign
    {
        $campaign = $this->campaignRepository->fetchById($campaignId);

        // Validações
        if ($campaign->status !== CampaignStatus::DRAFT) {
            throw new Exception("Only draft campaigns can be scheduled");
        }

        $scheduledAt = Carbon::parse($data['scheduled_at'], $data['timezone']);

        if ($scheduledAt <= now()) {
            throw new Exception("Scheduled time must be in the future");
        }

        // Atualizar campanha
        $campaign->scheduled_at = $scheduledAt;
        $campaign->status = CampaignStatus::SCHEDULED;
        $campaign->is_scheduled = true;

        return $this->campaignRepository->save($campaign);
    }
}
```

### 10.3 Solução para Endpoint de Teste

#### 10.3.1 Use Case TestSend
```php
class TestSend
{
    public function perform(int $campaignId, string $phoneNumber, array $testParams = []): array
    {
        $campaign = $this->campaignRepository->fetchFullById($campaignId);

        // Criar mensagem temporária para teste
        $testMessage = $this->createTestMessage($campaign, $phoneNumber, $testParams);

        // Enviar via WhatsApp
        $result = $this->whatsAppService->sendMessage($testMessage);

        // Log detalhado (não salvar na base)
        $this->logTestResult($campaign, $testMessage, $result);

        return [
            'success' => $result['success'],
            'message_id' => $result['message_id'] ?? null,
            'whatsapp_response' => $result['response'],
            'processed_message' => $testMessage->message,
            'sent_at' => now()->toISOString(),
            'error' => $result['error'] ?? null,
            'error_details' => $result['error_details'] ?? null,
            'suggestions' => $this->generateErrorSuggestions($result)
        ];
    }
}
```

### 10.4 Solução para Logs Avançados

#### 10.4.1 Migração para Novas Colunas
```php
Schema::table('messages', function (Blueprint $table) {
    $table->string('whatsapp_error_code')->nullable();
    $table->json('whatsapp_error_details')->nullable();
    $table->string('retry_strategy')->nullable();
    $table->text('final_failure_reason')->nullable();
    $table->json('whatsapp_response_log')->nullable();
});
```

#### 10.4.2 Enhanced Error Logging
```php
class EnhancedMessageLogger
{
    public function logWhatsAppError(Message $message, array $whatsAppResponse): void
    {
        $errorCode = $whatsAppResponse['error']['code'] ?? 'unknown';
        $errorType = $this->classifyError($errorCode);

        $message->update([
            'whatsapp_error_code' => $errorCode,
            'whatsapp_error_details' => $whatsAppResponse,
            'last_error_message' => $this->generateHumanReadableError($whatsAppResponse),
            'retry_strategy' => $this->determineRetryStrategy($errorType),
            'whatsapp_response_log' => $whatsAppResponse
        ]);

        // Sugestões automáticas
        $suggestions = $this->generateSuggestions($errorType, $whatsAppResponse);

        DBLog::logError(
            "WhatsApp message failed: {$errorType}",
            "EnhancedMessageLogger",
            $message->organization_id,
            null,
            [
                'message_id' => $message->id,
                'error_code' => $errorCode,
                'error_type' => $errorType,
                'suggestions' => $suggestions
            ]
        );
    }
}
```

## 11. Cronograma de Implementação

### 11.1 Sprint 1 - Correção de Status (2 semanas)
**Semana 1**:
- [ ] Implementar `SyncCampaignStatusFromMessages` job
- [ ] Corrigir lógica de `message_count`
- [ ] Adicionar testes unitários

**Semana 2**:
- [ ] Implementar `MonitorStuckCampaigns` job
- [ ] Corrigir `LaunchCampaign` para não definir `sent_at` prematuramente
- [ ] Deploy e monitoramento

### 11.2 Sprint 2 - Sistema de Agendamento (2 semanas)
**Semana 1**:
- [ ] Implementar endpoint `/schedule`
- [ ] Criar `ScheduleCampaign` use case
- [ ] Validações de timezone e data futura

**Semana 2**:
- [ ] Implementar `ProcessScheduledCampaigns` job
- [ ] Testes de integração
- [ ] Interface para agendamento

### 11.3 Sprint 3 - Endpoint de Teste (2 semanas)
**Semana 1**:
- [ ] Implementar `TestSend` use case
- [ ] Criar endpoint `/test-send`
- [ ] Sistema de logs em tempo real

**Semana 2**:
- [ ] Interface para visualizar resultados
- [ ] Testes de performance
- [ ] Documentação da API

### 11.4 Sprint 4 - Logs Avançados (2 semanas)
**Semana 1**:
- [ ] Migração para novas colunas
- [ ] Implementar `EnhancedMessageLogger`
- [ ] Classificação automática de erros

**Semana 2**:
- [ ] Dashboard de monitoramento
- [ ] Sistema de alertas
- [ ] Documentação completa

## 12. Considerações de Performance

### 12.1 Otimizações de Query
```sql
-- Índices necessários
CREATE INDEX idx_campaigns_status_org ON campaigns(organization_id, status);
CREATE INDEX idx_messages_campaign_status ON messages(campaign_id, status);
CREATE INDEX idx_messages_scheduled ON messages(scheduled_at, status);
```

### 12.2 Rate Limiting
- Jobs processam máximo 100 campanhas por execução
- Endpoint de teste limitado a 10 requests/minuto por organização
- Logs de erro com TTL de 30 dias

### 12.3 Monitoramento
- Métricas de performance dos jobs
- Alertas para jobs que falham consecutivamente
- Dashboard com tempo real de status das campanhas

## 13. Testes e Validação

### 13.1 Testes Unitários
- [ ] Todos os use cases com cobertura >90%
- [ ] Jobs com cenários de erro e sucesso
- [ ] Validações de regras de negócio

### 13.2 Testes de Integração
- [ ] Fluxo completo de agendamento
- [ ] Sincronização de status end-to-end
- [ ] Endpoint de teste com WhatsApp API real

### 13.3 Testes de Performance
- [ ] Jobs processando 1000+ campanhas
- [ ] Endpoint de teste com carga
- [ ] Stress test do sistema de logs

## 14. Documentação e Treinamento

### 14.1 Documentação Técnica
- [ ] API documentation atualizada
- [ ] Diagramas de fluxo dos novos processos
- [ ] Runbooks para troubleshooting

### 14.2 Documentação de Usuário
- [ ] Guia de uso do agendamento
- [ ] Tutorial do endpoint de teste
- [ ] FAQ sobre logs de erro

### 14.3 Treinamento da Equipe
- [ ] Sessão sobre novos jobs e monitoramento
- [ ] Workshop sobre debugging com novos logs
- [ ] Processo de escalação para problemas críticos
