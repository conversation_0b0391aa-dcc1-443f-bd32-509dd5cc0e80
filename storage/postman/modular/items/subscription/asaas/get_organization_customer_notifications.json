{"name": "Get Organization Customer Notifications", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/org-customer/{{organization_id}}/notifications", "host": ["{{URL}}"], "path": ["asaas", "org-customer", "{{organization_id}}", "notifications"]}}, "response": []}