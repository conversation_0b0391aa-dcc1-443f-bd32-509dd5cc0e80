{"name": "Update Organization Customer", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Organization Name\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+5511999999999\",\n  \"cpfCnpj\": \"12345678901234\"\n}"}, "url": {"raw": "{{URL}}/asaas/org-customer/{{organization_id}}", "host": ["{{URL}}"], "path": ["asaas", "org-customer", "{{organization_id}}"]}}, "response": []}