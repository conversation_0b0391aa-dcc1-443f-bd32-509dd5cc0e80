{"name": "Create <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"bot_id\": 1,\n  \"telegram_user_id\": 1,\n  \"chat_id\": -123456789,\n  \"from_id\": 987654321,\n  \"has_active_flow\": true,\n  \"has_broken_flow\": false,\n  \"current_flow\": \"atendimento_vendas\",\n  \"current_flow_id\": 1,\n  \"current_flow_status\": \"in_progress\",\n  \"current_step_id\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/telegram_chats", "host": ["{{URL}}"], "path": ["telegram_chats"]}}, "response": []}