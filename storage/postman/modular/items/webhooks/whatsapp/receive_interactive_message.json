{"name": "🎯 Receive Interactive Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "X-Hub-Signature-256", "value": "{{webhook_signature}}", "type": "text", "description": "HMAC SHA-256 signature for webhook security validation"}], "body": {"mode": "raw", "raw": "{\n    \"object\": \"whatsapp_business_account\",\n    \"entry\": [\n        {\n            \"id\": \"***************\",\n            \"changes\": [\n                {\n                    \"field\": \"messages\",\n                    \"value\": {\n                        \"messaging_product\": \"whatsapp\",\n                        \"metadata\": {\n                            \"display_phone_number\": \"+55 11 99999-9999\",\n                            \"phone_number_id\": \"{{phone_number_id}}\"\n                        },\n                        \"contacts\": [\n                            {\n                                \"profile\": {\n                                    \"name\": \"Ana Costa\"\n                                },\n                                \"wa_id\": \"*************\"\n                            }\n                        ],\n                        \"messages\": [\n                            {\n                                \"from\": \"*************\",\n                                \"id\": \"wamid.HBgLNTUxMTY2NjY2NjY2NhUCABIYIDdBNzA5QjI4RjA4NzRBNzk4Qjk2NzJGMDhBNzY4OUFB\",\n                                \"timestamp\": \"1699123456\",\n                                \"type\": \"interactive\",\n                                \"interactive\": {\n                                    \"type\": \"button_reply\",\n                                    \"button_reply\": {\n                                        \"id\": \"MENU_OPTION_PRODUCTS\",\n                                        \"title\": \"Ver Produtos\"\n                                    }\n                                },\n                                \"context\": {\n                                    \"from\": \"5511999999999\",\n                                    \"id\": \"wamid.HBgLNTUxMTk5OTk5OTk5OBUCABIYIDdBNzA5QjI4RjA4NzRBNzk4Qjk2NzJGMDhBNzY4OUFB\"\n                                }\n                            }\n                        ]\n                    }\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/whatsapp/webhook", "host": ["{{URL}}"], "path": ["whatsapp", "webhook"]}, "description": "**Interactive Message Webhook**\n\nThis request simulates receiving an interactive message (button reply) from a WhatsApp user. Interactive messages are responses to buttons, lists, or other interactive elements sent by your business.\n\n**Interactive Message Features:**\n- 🎯 Button reply processing\n- 🔗 Context message linking (reply to specific message)\n- 🤖 ChatBot flow continuation\n- 📊 User interaction tracking\n\n**Message Structure:**\n- **Type**: `interactive`\n- **Subtype**: `button_reply`\n- **Button ID**: `MENU_OPTION_PRODUCTS`\n- **Button Title**: `Ver Produtos`\n- **Context**: Links to original message that contained the buttons\n\n**Processing Features:**\n- 🔐 Signature validation\n- 🏢 Organization identification\n- 🎯 Interactive element processing\n- 🔗 Context message handling\n- 🤖 ChatBot flow navigation\n- 📝 Interaction logging\n\n**ChatBot Integration:**\nThe ChatBot can use the button ID to:\n- 🎯 Identify user selection\n- 🔄 Continue conversation flow\n- 📋 Execute specific actions\n- 📊 Track user preferences\n\n**Variables needed:**\n- `{{webhook_signature}}`: HMAC SHA-256 signature\n- `{{phone_number_id}}`: Your WhatsApp Business phone number ID\n- `{{URL}}`: Your API base URL\n\n**Expected Response:**\n```json\n{\n    \"status\": \"success\",\n    \"processed\": 1,\n    \"results\": [\n        {\n            \"success\": true,\n            \"type\": \"message\",\n            \"processed\": 1,\n            \"organization_id\": 1,\n            \"phone_number_id\": \"123456789\",\n            \"results\": [\n                {\n                    \"success\": true,\n                    \"message_id\": \"interactive_msg_1\",\n                    \"button_id\": \"MENU_OPTION_PRODUCTS\",\n                    \"chatbot_response\": \"...\"\n                }\n            ]\n        }\n    ]\n}\n```\n\n**Use Cases:**\n- 🎯 Testing interactive button flows\n- 🤖 ChatBot menu navigation\n- 📊 User interaction analytics\n- 🔄 Conversation flow testing\n- 🎨 UI/UX validation\n\n**Other Interactive Types:**\nYou can also test:\n- 📋 List replies (`list_reply`)\n- 📍 Location messages\n- 📞 Contact sharing\n- 🎵 Audio messages"}, "response": []}