{"name": "Webhook Verify (Alternative)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp/webhook?hub.mode=subscribe&hub.challenge={{challenge_token}}&hub.verify_token={{verify_token}}", "host": ["{{URL}}"], "path": ["whatsapp", "webhook"], "query": [{"key": "hub.mode", "value": "subscribe", "description": "Webhook verification mode"}, {"key": "hub.challenge", "value": "{{challenge_token}}", "description": "Challenge token from <PERSON><PERSON>"}, {"key": "hub.verify_token", "value": "{{verify_token}}", "description": "Your webhook verify token (uses services.meta.webhook_verify_token config)"}]}}, "response": []}