{"name": "❌ Test Missing Signature", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"object\": \"whatsapp_business_account\",\n    \"entry\": [\n        {\n            \"id\": \"***************\",\n            \"changes\": [\n                {\n                    \"field\": \"messages\",\n                    \"value\": {\n                        \"messaging_product\": \"whatsapp\",\n                        \"metadata\": {\n                            \"display_phone_number\": \"+55 11 99999-9999\",\n                            \"phone_number_id\": \"{{phone_number_id}}\"\n                        },\n                        \"messages\": [\n                            {\n                                \"from\": \"*************\",\n                                \"id\": \"wamid.test_no_signature\",\n                                \"timestamp\": \"**********\",\n                                \"type\": \"text\",\n                                \"text\": {\n                                    \"body\": \"This message should be rejected due to missing signature\"\n                                }\n                            }\n                        ]\n                    }\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/whatsapp/webhook", "host": ["{{URL}}"], "path": ["whatsapp", "webhook"]}, "description": "**Security Test: Missing Signature**\n\nThis request tests the webhook security validation by sending a request without the required X-Hub-Signature-256 header. The endpoint should reject this request with a 403 Forbidden response.\n\n**Security Features Tested:**\n- 🔐 Required signature header validation\n- 🛡️ Protection against unsigned requests\n- 📝 Security event logging\n- 🚫 Request rejection for missing signatures\n\n**Missing Header:**\n- `X-Hub-Signature-256`: Not included in request\n\n**Expected Behavior:**\n1. ❌ Signature header missing\n2. 🚫 Request rejected with 403 status\n3. 📝 Security log created with 'missing' signature\n4. 🔒 No message processing occurs\n\n**Expected Response:**\n```json\n{\n    \"error\": \"Forbidden\"\n}\n```\n\n**Security Logging:**\nA log entry will be created with:\n- `event_type`: 'security'\n- `processing_status`: 'failed'\n- `error_message`: 'Invalid signature'\n- `webhook_payload.signature`: 'missing'\n\n**Variables needed:**\n- `{{phone_number_id}}`: Your WhatsApp Business phone number ID\n- `{{URL}}`: Your API base URL\n\n**Use Cases:**\n- 🧪 Testing webhook security requirements\n- 🔍 Validating signature requirement enforcement\n- 📊 Monitoring unauthorized access attempts\n- 🛡️ Ensuring all requests must be signed\n\n**Note:**\nIn production, Meta will always include the X-Hub-Signature-256 header. This test simulates potential malicious requests or misconfigured webhooks."}, "response": []}