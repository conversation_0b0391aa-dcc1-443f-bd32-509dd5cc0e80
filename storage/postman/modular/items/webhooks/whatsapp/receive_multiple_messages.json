{"name": "📨📨 Receive Multiple Messages", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "X-Hub-Signature-256", "value": "{{webhook_signature}}", "type": "text", "description": "HMAC SHA-256 signature for webhook security validation"}], "body": {"mode": "raw", "raw": "{\n    \"object\": \"whatsapp_business_account\",\n    \"entry\": [\n        {\n            \"id\": \"***************\",\n            \"changes\": [\n                {\n                    \"field\": \"messages\",\n                    \"value\": {\n                        \"messaging_product\": \"whatsapp\",\n                        \"metadata\": {\n                            \"display_phone_number\": \"+55 11 99999-9999\",\n                            \"phone_number_id\": \"{{phone_number_id}}\"\n                        },\n                        \"contacts\": [\n                            {\n                                \"profile\": {\n                                    \"name\": \"<PERSON>\"\n                                },\n                                \"wa_id\": \"*************\"\n                            },\n                            {\n                                \"profile\": {\n                                    \"name\": \"<PERSON>\"\n                                },\n                                \"wa_id\": \"5511888888888\"\n                            }\n                        ],\n                        \"messages\": [\n                            {\n                                \"from\": \"*************\",\n                                \"id\": \"wamid.HBgLNTUxMTc3Nzc3Nzc3NxUCABIYIDdBNzA5QjI4RjA4NzRBNzk4Qjk2NzJGMDhBNzY4OUFB\",\n                                \"timestamp\": \"1699123456\",\n                                \"type\": \"text\",\n                                \"text\": {\n                                    \"body\": \"Primeira mensagem do usuário\"\n                                }\n                            },\n                            {\n                                \"from\": \"5511888888888\",\n                                \"id\": \"wamid.HBgLNTUxMTg4ODg4ODg4OBUCABIYIDdBNzA5QjI4RjA4NzRBNzk4Qjk2NzJGMDhBNzY4OUFB\",\n                                \"timestamp\": \"1699123460\",\n                                \"type\": \"text\",\n                                \"text\": {\n                                    \"body\": \"Segunda mensagem de outro usuário\"\n                                }\n                            },\n                            {\n                                \"from\": \"*************\",\n                                \"id\": \"wamid.HBgLNTUxMTc3Nzc3Nzc3NxUCABIYIDdBNzA5QjI4RjA4NzRBNzk4Qjk2NzJGMDhBNzY4OUFB\",\n                                \"timestamp\": \"1699123465\",\n                                \"type\": \"button\",\n                                \"button\": {\n                                    \"payload\": \"BUTTON_OPTION_1\",\n                                    \"text\": \"Opção 1\"\n                                }\n                            }\n                        ]\n                    }\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/whatsapp/webhook", "host": ["{{URL}}"], "path": ["whatsapp", "webhook"]}, "description": "**Multiple Messages Webhook Test**\n\nThis request simulates receiving multiple messages in a single webhook call, which can happen when Meta batches messages or when multiple users send messages simultaneously.\n\n**Features Tested:**\n- 📨 Multiple message processing\n- 👥 Multiple contacts handling\n- 🔄 Sequential message processing\n- 🤖 ChatBot integration for each message\n- 📊 Batch processing statistics\n\n**Message Types Included:**\n1. 💬 Text message from User 1\n2. 💬 Text message from User 2  \n3. 🔘 Button response from User 1\n\n**Processing Features:**\n- 🏢 Organization identification via phone_number_id\n- 🔐 Signature validation for security\n- 🚫 Outgoing message filtering (if any)\n- 📝 Individual message logging\n- 🤖 ChatBot response generation\n\n**Expected Processing:**\n1. ✅ Signature validation\n2. ✅ Payload structure validation\n3. 🏢 Organization identification\n4. 📨 Process 3 incoming messages\n5. 🤖 Generate ChatBot responses\n6. 📊 Return processing statistics\n\n**Variables needed:**\n- `{{webhook_signature}}`: HMAC SHA-256 signature\n- `{{phone_number_id}}`: Your WhatsApp Business phone number ID\n- `{{URL}}`: Your API base URL\n\n**Expected Response:**\n```json\n{\n    \"status\": \"success\",\n    \"processed\": 1,\n    \"results\": [\n        {\n            \"success\": true,\n            \"type\": \"message\",\n            \"processed\": 3,\n            \"organization_id\": 1,\n            \"phone_number_id\": \"123456789\",\n            \"results\": [\n                {\"success\": true, \"message_id\": \"msg1\"},\n                {\"success\": true, \"message_id\": \"msg2\"},\n                {\"success\": true, \"message_id\": \"msg3\"}\n            ]\n        }\n    ]\n}\n```\n\n**Use Cases:**\n- 🧪 Testing batch message processing\n- 📊 Performance testing with multiple messages\n- 🤖 ChatBot conversation flow testing\n- 👥 Multi-user interaction simulation"}, "response": []}