{"name": "Receive Message (Alternative)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"object\": \"whatsapp_business_account\",\n    \"entry\": [\n        {\n            \"id\": \"WHATSAPP_BUSINESS_ACCOUNT_ID\",\n            \"changes\": [\n                {\n                    \"value\": {\n                        \"messaging_product\": \"whatsapp\",\n                        \"metadata\": {\n                            \"display_phone_number\": \"***********\",\n                            \"phone_number_id\": \"*********\"\n                        },\n                        \"contacts\": [\n                            {\n                                \"profile\": {\n                                    \"name\": \"<PERSON>\"\n                                },\n                                \"wa_id\": \"*************\"\n                            }\n                        ],\n                        \"messages\": [\n                            {\n                                \"from\": \"*************\",\n                                \"id\": \"wamid.xxx\",\n                                \"timestamp\": \"**********\",\n                                \"text\": {\n                                    \"body\": \"Hello WhatsApp Bot (Alternative)!\"\n                                },\n                                \"type\": \"text\"\n                            }\n                        ]\n                    },\n                    \"field\": \"messages\"\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/whatsapp/webhook", "host": ["{{URL}}"], "path": ["whatsapp", "webhook"]}}, "response": []}