{"name": "Create Project from Budget", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Project from Budget\",\n  \"description\": \"Project created from budget\",\n  \"client_id\": 1\n}"}, "url": {"raw": "{{URL}}/project/budget/{{budget_id}}", "host": ["{{URL}}"], "path": ["project", "budget", "{{budget_id}}"]}}, "response": []}