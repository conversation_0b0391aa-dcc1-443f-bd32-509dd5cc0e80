{"name": "Add Products to Budget", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"products\": [\n    {\n      \"product_id\": 1,\n      \"quantity\": 10,\n      \"unit_price\": 25.50\n    },\n    {\n      \"product_id\": 2,\n      \"quantity\": 5,\n      \"unit_price\": 15.00\n    }\n  ]\n}"}, "url": {"raw": "{{URL}}/budget/{{budget_id}}/products/", "host": ["{{URL}}"], "path": ["budget", "{{budget_id}}", "products", ""]}}, "response": []}