{"name": "Process Batch at Stock", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"stock_id\": 1,\n  \"process_type\": \"entry\",\n  \"notes\": \"Processing batch at stock\"\n}"}, "url": {"raw": "{{URL}}/batch/{{batch_id}}/process-at-stock/", "host": ["{{URL}}"], "path": ["batch", "{{batch_id}}", "process-at-stock", ""]}}, "response": []}