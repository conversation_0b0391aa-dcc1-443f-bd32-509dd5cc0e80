{"name": "Get All Permissions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/permissions?name=&slug=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["permissions"], "query": [{"key": "name", "value": "", "description": "Filter by permission name"}, {"key": "slug", "value": "", "description": "Filter by permission slug"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}