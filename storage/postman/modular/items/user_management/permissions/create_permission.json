{"name": "Create Permission", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Manage Users\",\n  \"slug\": \"manage-users\",\n  \"description\": \"Permission to manage users in the system\"\n}"}, "url": {"raw": "{{URL}}/permissions", "host": ["{{URL}}"], "path": ["permissions"]}}, "response": []}