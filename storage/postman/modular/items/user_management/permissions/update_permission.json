{"name": "Update Permission", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Manage Users Updated\",\n  \"slug\": \"manage-users-updated\",\n  \"description\": \"Updated permission to manage users in the system\"\n}"}, "url": {"raw": "{{URL}}/permissions/{{permission_id}}", "host": ["{{URL}}"], "path": ["permissions", "{{permission_id}}"]}}, "response": []}