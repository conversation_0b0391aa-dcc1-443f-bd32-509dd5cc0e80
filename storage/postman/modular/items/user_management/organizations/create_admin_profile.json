{"name": "Create Admin Profile", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Admin Profile\",\n  \"description\": \"Administrator profile with full permissions\",\n  \"permissions\": [1, 2, 3, 4, 5]\n}"}, "url": {"raw": "{{URL}}/organization/{{organization_id}}/create-admin-profile", "host": ["{{URL}}"], "path": ["organization", "{{organization_id}}", "create-admin-profile"]}}, "response": []}