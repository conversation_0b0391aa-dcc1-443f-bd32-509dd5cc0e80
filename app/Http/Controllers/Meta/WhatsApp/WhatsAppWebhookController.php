<?php

namespace App\Http\Controllers\Meta\WhatsApp;

use App\Domains\WhatsApp\ChangeValue;
use App\Helpers\DBLog;
use App\Helpers\Traits\Response;
use App\Http\Controllers\Controller;
use App\Services\Meta\WhatsApp\UseCases\Webhook\ExtractPhoneNumberIdFromWebhook;
use App\Services\Meta\WhatsApp\UseCases\Webhook\ProcessWebhook;
use App\Services\Meta\WhatsApp\UseCases\Webhook\ValidateWebhookPayload;
use App\Services\Meta\WhatsApp\UseCases\Webhook\ValidateWebhookSignatureWithOrganization;
use App\UseCases\ChatBot\PhoneNumber\Get;
use App\UseCases\Organization\FetchOrganizationFromPhoneNumber;
use App\UseCases\Organization\VerifyWebhookToken;
use App\UseCases\WhatsAppWebhookLog\LogWebhookEvent;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class WhatsAppWebhookController extends Controller
{
    use Response;

    /**
     * Handle WhatsApp webhook verification (GET request)
     * @throws Exception
     */
    public function verify(Request $request): JsonResponse
    {
        $mode = $request->query('hub_mode');
        $token = $request->query('hub_verify_token');
        $challenge = $request->query('hub_challenge');

        /** @var VerifyWebhookToken $verifyWebhookToken */
        $verifyWebhookToken = app()->make(VerifyWebhookToken::class);
        $verifyResult = $verifyWebhookToken->perform($mode, $token);

        if ($verifyResult['success']) {
            /** @var LogWebhookEvent $logWebhookEvent */
            $logWebhookEvent = app()->make(LogWebhookEvent::class);
            $logWebhookEvent->perform(
                $verifyResult['organization']?->id,
                null,
                'verification',
                ['type' => $verifyResult['type']],
                'success'
            );

            return response()->json((int) $challenge);
        }

        /** @var LogWebhookEvent $logWebhookEvent */
        $logWebhookEvent = app()->make(LogWebhookEvent::class);
        $logWebhookEvent->perform(
            null,
            null,
            'verification',
            ['token' => substr($token, 0, 8) . '...', 'mode' => $mode],
            'failed',
            $verifyResult['error']
        );

        return response()->json(['error' => 'Forbidden'], 403);
    }

    /**
     * Handle WhatsApp webhook events (POST request) - Refactored with UseCases
     * @throws Exception
     */
    public function handle(Request $request): JsonResponse
    {
        try {
            DBLog::log(
                "WhatsApp webhook received",
                "WhatsApp::WhatsAppWebhookController::handle",
                null,
                null,
                ['payload' => $request->all()]
            );

            $signature = $request->header('X-Hub-Signature-256');
            $payload = $request->getContent();
            $webhookData = $request->all();

            /** @var ExtractPhoneNumberIdFromWebhook $extractPhoneNumberId */
            $extractPhoneNumberId = app()->make(ExtractPhoneNumberIdFromWebhook::class);
            $phoneNumberId = $extractPhoneNumberId->perform($webhookData);

            if ($phoneNumberId === null) {
                DBLog::logError(
                    "WhatsApp webhook: Could not extract phone_number_id from payload",
                    "WhatsApp::WhatsAppWebhookController::handle",
                    null,
                    null,
                    ['payload' => $webhookData]
                );
                return response()->json(['status' => 'success', 'message' => 'Webhook processed'], 200);
            }

            /** @var FetchOrganizationFromPhoneNumber $fetchOrganizationFromPhoneNumber */
            $fetchOrganizationFromPhoneNumber = app()->make(FetchOrganizationFromPhoneNumber::class);
            $fetch = $fetchOrganizationFromPhoneNumber->perform($phoneNumberId);

            $organization = $fetch['organization'] ?? null;
            $phoneNumber = $fetch['phone_number'] ?? null;

            if ($organization === null || $phoneNumber === null) {
                DBLog::logError(
                    "WhatsApp webhook: Could not find organization or phone number for phone_number_id: {$phoneNumberId}",
                    "WhatsApp::WhatsAppWebhookController::handle",
                    null,
                    null,
                    ['phone_number_id' => $phoneNumberId]
                );
                return response()->json(['status' => 'success', 'message' => 'Webhook processed'], 200);
            }

            /** @var ValidateWebhookSignatureWithOrganization $validateSignatureWithOrg */
            $validateSignatureWithOrg = app()->make(ValidateWebhookSignatureWithOrganization::class);
            $validationResult = $validateSignatureWithOrg->perform($payload, $signature, $phoneNumberId, $organization);

            if (!$validationResult['success']) {
                /** @var LogWebhookEvent $logWebhookEvent */
                $logWebhookEvent = app()->make(LogWebhookEvent::class);
                $logWebhookEvent->perform(
                    $validationResult['organization']?->id,
                    $phoneNumberId,
                    'security',
                    ['signature' => $signature ? substr($signature, 0, 20) . '...' : 'missing'],
                    'failed',
                    $validationResult['error'] ?? 'Invalid signature'
                );

                return response()->json(['status' => 'success', 'message' => 'Webhook processed'], 200);
            }

            /** @var ValidateWebhookPayload $validateWebhookPayload */
            $validateWebhookPayload = app()->make(ValidateWebhookPayload::class);
            if (!$validateWebhookPayload->perform($webhookData)) {
                $logWebhookEvent = app()->make(LogWebhookEvent::class);
                $logWebhookEvent->perform(
                    null,
                    null,
                    'other',
                    ['error' => 'Invalid webhook data structure'],
                    'failed',
                    'Invalid webhook data'
                );

                // ALWAYS return success to WhatsApp to prevent retries
                return response()->json(['status' => 'success', 'message' => 'Webhook processed'], 200);
            }

            $results = [];

            /** @var LogWebhookEvent $logWebhookEvent */
            $logWebhookEvent = app()->make(LogWebhookEvent::class);
            $webhookLog = $logWebhookEvent->perform(
                $organization->id,
                $phoneNumberId,
                'generic',
                $webhookData,
            );

            DBLog::log(
                "WhatsApp webhook payload validated",
                "WhatsApp::WhatsAppWebhookController::validateWebhookPayload",
                null,
                null,
                ['webhookData' => $webhookData, 'webhook_log_id' => $webhookLog->id]
            );

            foreach ($webhookData['entry'] ?? [] as $entry) {
                foreach ($entry['changes'] ?? [] as $change) {
                    if ($change['field'] === 'messages') {
                        /** @var ProcessWebhook $useCaseProcess */
                        $useCaseProcess = app()->make(ProcessWebhook::class);
                        $result = $useCaseProcess->perform(new ChangeValue($change['value']), $organization, $phoneNumber);
                        if ($result) {
                            $results[] = $result;
                        }
                    }
                }
            }

            if (!empty($results)) {
                $webhookLog->markAsSuccessful();
                $logWebhookEvent->markAsSuccessful($webhookLog->id);
            }

            return response()->json([
                'status' => 'success',
                'processed' => count($results),
                'results' => $results
            ]);

        } catch (Exception $e) {
            DBLog::logError(
                "WhatsApp webhook processing failed",
                "WhatsApp::WhatsAppWebhookController::handle",
                null,
                null,
                ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]
            );

            /** @var LogWebhookEvent $logWebhookEvent */
            $logWebhookEvent = app()->make(LogWebhookEvent::class);
            $logWebhookEvent->perform(
                null, null, 'other', $request->all(), 'failed', $e->getMessage()
            );

            return response()->json([
                'status' => 'success',
                'message' => 'Webhook processed'
            ], 200);
        }
    }

    public function testWhatsAppToken(int $id) : JsonResponse
    {
        /** @var Get $useCase */
        $useCase = app()->make(Get::class);
        $phoneNumber = $useCase->perform($id);

        $accessToken = $phoneNumber->whatsapp_access_token;
        $phoneNumberId = $phoneNumber->whatsapp_phone_number_id;

        $url = "https://graph.facebook.com/v23.0/{$phoneNumberId}/whatsapp_business_profile";

        $response = Http::withToken(
            $accessToken
        )->get($url);

        if ($response->successful()) {
            return $this->response(
                "Whatsapp phone number is fully integrated",
                "success",
                200,
                $response->json()
            );
        } else {
            $response = Http::withToken($accessToken)->get('https://graph.facebook.com/v23.0/me');
            if (!$response->successful()) {
                return $this->response(
                    "Whatsapp phone number is not fully integrated even with me: " . $url,
                    "error",
                    401,
                    $response->json()
                );
            }
            return $this->response(
                "Whatsapp phone number is not fully integrated: " . $url,
                "error",
                401,
                $response->json()
            );
        }
    }
}
